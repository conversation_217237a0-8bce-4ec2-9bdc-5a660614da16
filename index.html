<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>V2Board Dashboard - HeroUI Theme</title>
    <!-- 引入 Tailwind CSS for styling -->
    <script src="https://cdn.tailwindcss.com"></script>
    <!-- 引入 Ionicons for icons (Heroicons is a commercial product, using a free alternative) -->
    <script type="module" src="https://unpkg.com/ionicons@5.5.2/dist/ionicons/ionicons.esm.js"></script>
    <script nomodule src="https://unpkg.com/ionicons@5.5.2/dist/ionicons/ionicons.js"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <style>
        /* Custom Styles */
        body {
            font-family: 'Inter', sans-serif;
            background-color: #f3f4f6; /* Light gray background */
        }
        .card {
            background-color: white;
            border-radius: 1rem;
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
            transition: all 0.3s ease-in-out;
        }
        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        }
        .btn {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            padding: 0.75rem 1.5rem;
            border-radius: 0.75rem;
            font-weight: 600;
            transition: all 0.2s ease-in-out;
            cursor: pointer;
        }
        .btn-primary {
            background-color: #4f46e5;
            color: white;
        }
        .btn-primary:hover {
            background-color: #4338ca;
        }
        .btn-secondary {
            background-color: #e5e7eb;
            color: #1f2937;
        }
        .btn-secondary:hover {
            background-color: #d1d5db;
        }
        .progress-bar-bg {
            background-color: #e5e7eb;
            border-radius: 9999px;
            overflow: hidden;
        }
        .progress-bar-fg {
            height: 100%;
            border-radius: 9999px;
            background-color: #4f46e5;
            transition: width 0.5s ease-in-out;
            text-align: center;
            color: white;
            font-size: 0.75rem;
            line-height: 1rem;
        }
        .toast {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 1rem 1.5rem;
            border-radius: 0.75rem;
            color: white;
            z-index: 50;
            opacity: 0;
            transition: opacity 0.3s ease-in-out, transform 0.3s ease-in-out;
            transform: translateY(-20px);
        }
        .toast.show {
            opacity: 1;
            transform: translateY(0);
        }
        .toast.success { background-color: #10b981; }
        .toast.error { background-color: #ef4444; }
    </style>
</head>
<body class="antialiased">
    <div id="toast-container" class="toast"></div>

    <div class="flex h-screen bg-gray-100">
        <!-- Sidebar -->
        <div class="hidden md:flex flex-col w-64 bg-white shadow-lg">
            <div class="flex items-center justify-center h-20 border-b">
                <h1 class="text-2xl font-bold text-indigo-600">My Dashboard</h1>
            </div>
            <div class="flex-grow p-4">
                <nav>
                    <a href="#" class="flex items-center px-4 py-3 text-white bg-indigo-600 rounded-lg">
                        <ion-icon name="home-outline" class="text-xl"></ion-icon>
                        <span class="mx-4 font-medium">仪表盘</span>
                    </a>
                    <a href="#" class="flex items-center px-4 py-3 mt-4 text-gray-600 rounded-lg hover:bg-gray-200">
                        <ion-icon name="server-outline" class="text-xl"></ion-icon>
                        <span class="mx-4 font-medium">节点列表</span>
                    </a>
                    <a href="#" class="flex items-center px-4 py-3 mt-4 text-gray-600 rounded-lg hover:bg-gray-200">
                        <ion-icon name="cart-outline" class="text-xl"></ion-icon>
                        <span class="mx-4 font-medium">购买订阅</span>
                    </a>
                    <a href="profile.html" class="flex items-center px-4 py-3 mt-4 text-gray-600 rounded-lg hover:bg-gray-200">
                        <ion-icon name="person-outline" class="text-xl"></ion-icon>
                        <span class="mx-4 font-medium">我的账户</span>
                    </a>
                </nav>
            </div>
        </div>

        <!-- Main content -->
        <div class="flex flex-col flex-1 overflow-y-auto">
            <header class="flex items-center justify-between h-20 px-6 bg-white border-b">
                <div class="flex items-center">
                    <h2 class="text-2xl font-semibold text-gray-800">欢迎回来, <span id="user-email-header">...</span></h2>
                </div>
                <div class="flex items-center">
                    <button class="text-gray-500 hover:text-gray-700 focus:outline-none">
                        <ion-icon name="notifications-outline" class="text-2xl"></ion-icon>
                    </button>
                    <div class="relative ml-4">
                        <button class="flex items-center focus:outline-none">
                            <span id="user-email" class="text-right">...</span>
                            <img class="w-8 h-8 ml-2 rounded-full object-cover" src="https://placehold.co/100x100/663399/FFFFFF?text=U" alt="用户头像">
                        </button>
                    </div>
                </div>
            </header>
            
            <main class="flex-1 p-6 md:p-10">
                <!-- API Login Form -->
                <div id="login-section" class="card p-6 mb-8">
                    <h3 class="text-xl font-semibold mb-4">登录您的账户</h3>
                    <p class="text-gray-600 mb-4">请输入您的 V2Board 网站地址、邮箱和密码以加载数据。</p>
                    <div class="space-y-4">
                         <input type="text" id="v2board-url" placeholder="V2Board 地址 (例如: https://myv2.com)" class="w-full px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500">
                         <input type="email" id="email-input" placeholder="您的邮箱" class="w-full px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500">
                         <input type="password" id="password-input" placeholder="您的密码" class="w-full px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500">
                         <button id="login-btn" class="btn btn-primary w-full">登录并加载数据</button>
                    </div>
                </div>

                <!-- Loading Spinner -->
                <div id="loading-spinner" class="hidden text-center py-10">
                    <svg class="animate-spin h-10 w-10 text-indigo-600 mx-auto" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    <p class="mt-4 text-gray-600">正在登录并加载数据...</p>
                </div>
                
                <!-- Main Dashboard Content -->
                <div id="dashboard-content" class="hidden">
                    <!-- Subscription Info -->
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <div class="card p-6 flex flex-col justify-between">
                            <div>
                                <div class="flex items-center">
                                    <div class="p-3 bg-indigo-100 rounded-full">
                                        <ion-icon name="rocket-outline" class="text-2xl text-indigo-600"></ion-icon>
                                    </div>
                                    <h4 class="ml-4 text-lg font-semibold text-gray-700">我的订阅</h4>
                                </div>
                                <p id="plan-name" class="mt-4 text-3xl font-bold text-gray-900">...</p>
                            </div>
                            <p id="plan-expire" class="mt-2 text-sm text-gray-500">到期时间: ...</p>
                        </div>
                        <div class="card p-6 flex flex-col justify-between">
                            <div>
                                <div class="flex items-center">
                                    <div class="p-3 bg-green-100 rounded-full">
                                        <ion-icon name="time-outline" class="text-2xl text-green-600"></ion-icon>
                                    </div>
                                    <h4 class="ml-4 text-lg font-semibold text-gray-700">剩余时间</h4>
                                </div>
                                <p id="days-left" class="mt-4 text-3xl font-bold text-gray-900">...</p>
                            </div>
                            <p class="mt-2 text-sm text-gray-500">剩余可用天数</p>
                        </div>
                        <div class="card p-6 flex flex-col justify-between">
                             <div>
                                <div class="flex items-center">
                                    <div class="p-3 bg-blue-100 rounded-full">
                                        <ion-icon name="swap-vertical-outline" class="text-2xl text-blue-600"></ion-icon>
                                    </div>
                                    <h4 class="ml-4 text-lg font-semibold text-gray-700">流量重置日</h4>
                                </div>
                                <p id="reset-day" class="mt-4 text-3xl font-bold text-gray-900">...</p>
                            </div>
                            <p id="reset-day-info" class="mt-2 text-sm text-gray-500">...</p>
                        </div>
                    </div>

                    <!-- Traffic Usage -->
                    <div class="card p-6 mt-8">
                        <h3 class="text-xl font-semibold mb-2">流量使用情况</h3>
                        <p class="text-sm text-gray-500 mb-4">已用 <span id="traffic-used" class="font-bold">...</span> / 总计 <span id="traffic-total" class="font-bold">...</span></p>
                        <div class="progress-bar-bg h-4 w-full">
                            <div id="traffic-progress" class="progress-bar-fg" style="width: 0%;"></div>
                        </div>
                        <p id="traffic-info" class="text-right text-sm text-gray-600 mt-2">剩余流量: ...</p>
                    </div>

                    <!-- Quick Actions -->
                    <div class="card p-6 mt-8">
                        <h3 class="text-xl font-semibold mb-4">快捷操作</h3>
                        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                             <button id="copy-sub-link-btn" class="btn btn-primary">
                                <ion-icon name="copy-outline" class="mr-2"></ion-icon>
                                <span>一键复制订阅</span>
                            </button>
                            <button class="btn btn-secondary">
                                <ion-icon name="cloud-download-outline" class="mr-2"></ion-icon>
                                <span>客户端下载</span>
                            </button>
                            <button class="btn btn-secondary">
                                <ion-icon name="book-outline" class="mr-2"></ion-icon>
                                <span>使用文档</span>
                            </button>
                            <button class="btn btn-secondary">
                                <ion-icon name="logo-apple-appstore" class="mr-2"></ion-icon>
                                <span>转到商店</span>
                            </button>
                        </div>
                         <input type="text" id="sub-link-input" class="opacity-0 absolute -z-10">
                    </div>
                </div>
            </main>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // --- DOM Elements ---
            const v2boardUrlInput = document.getElementById('v2board-url');
            const emailInput = document.getElementById('email-input');
            const passwordInput = document.getElementById('password-input');
            const loginBtn = document.getElementById('login-btn');
            const loadingSpinner = document.getElementById('loading-spinner');
            const dashboardContent = document.getElementById('dashboard-content');
            const loginSection = document.getElementById('login-section');

            // --- Local Storage for convenience ---
            v2boardUrlInput.value = localStorage.getItem('v2board_url') || '';
            emailInput.value = localStorage.getItem('v2board_email') || '';

            // --- Event Listeners ---
            loginBtn.addEventListener('click', handleLogin);

            /**
             * Handles the login process.
             */
            async function handleLogin() {
                const baseUrl = v2boardUrlInput.value.trim();
                const email = emailInput.value.trim();
                const password = passwordInput.value.trim();

                if (!baseUrl || !email || !password) {
                    showToast('请填写所有登录字段', 'error');
                    return;
                }
                
                // Save info to local storage
                localStorage.setItem('v2board_url', baseUrl);
                localStorage.setItem('v2board_email', email);

                // --- UI State Updates ---
                dashboardContent.classList.add('hidden');
                loadingSpinner.classList.remove('hidden');

                try {
                    const loginUrl = new URL('/api/v1/passport/auth/login', baseUrl).href;
                    const loginResponse = await fetch(loginUrl, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({ email, password }),
                    });

                    if (!loginResponse.ok) {
                         const errorData = await loginResponse.json();
                         throw new Error(errorData.message || `登录失败，状态码: ${loginResponse.status}`);
                    }

                    const loginResult = await loginResponse.json();

                    if (loginResult.data && loginResult.data.auth_data) {
                        const authToken = loginResult.data.auth_data;
                        await fetchDashboardData(baseUrl, authToken);
                    } else {
                        throw new Error('登录成功，但未返回认证凭证');
                    }

                } catch (error) {
                    console.error('Login process failed:', error);
                    showToast(error.message, 'error');
                    loadingSpinner.classList.add('hidden');
                }
            }
            
            /**
             * Fetches and displays the main dashboard data after successful login.
             * @param {string} baseUrl - The base URL of the V2Board instance
             * @param {string} authToken - The full "Bearer ..." authentication token
             */
            async function fetchDashboardData(baseUrl, authToken) {
                try {
                    // REFACTORED: Now only one API call is needed.
                    const subscribeData = await fetchApi('/api/v1/user/getSubscribe', baseUrl, authToken);
                    
                    if (subscribeData) {
                        updateDashboardUI(subscribeData);
                        dashboardContent.classList.remove('hidden');
                        loginSection.classList.add('hidden'); // Hide login form after successful load
                    }
                } catch (error) {
                    console.error('Error loading dashboard data:', error);
                    showToast(`加载仪表盘数据失败: ${error.message}`, 'error');
                    // Show login section again if dashboard fetch fails
                    loginSection.classList.remove('hidden');
                } finally {
                    loadingSpinner.classList.add('hidden');
                }
            }


            /**
             * A generic fetch wrapper for the V2Board API.
             * @param {string} endpoint - The API endpoint.
             * @param {string} baseUrl - The base URL of the V2Board instance.
             * @param {string} token - The full authorization header value (e.g., "Bearer ...").
             * @returns {Promise<Object|null>} The JSON data from the API.
             */
            async function fetchApi(endpoint, baseUrl, token) {
                const url = new URL(endpoint, baseUrl).href;
                try {
                    const response = await fetch(url, {
                        headers: {
                            'Authorization': token 
                        }
                    });

                    if (!response.ok) {
                        if (response.status === 401) throw new Error('认证失败 (Unauthorized)');
                        throw new Error(`HTTP 错误! 状态: ${response.status}`);
                    }
                    
                    const result = await response.json();
                    if (result.data) {
                        return result.data;
                    } else {
                         const errorMsg = result.message || 'API 返回数据格式不正确';
                         throw new Error(errorMsg);
                    }
                } catch (error) {
                    console.error(`Fetch error for ${endpoint}:`, error);
                    // Let the calling function handle the toast for better context.
                    throw error;
                }
            }
            
            /**
             * REFACTORED: Updates the dashboard UI with the fetched data from the single subscribe endpoint.
             * @param {Object} data - Data from the /user/getSubscribe endpoint.
             */
            function updateDashboardUI(data) {
                // --- User Info ---
                document.getElementById('user-email').textContent = data.email;
                document.getElementById('user-email-header').textContent = data.email;

                // --- Plan Info ---
                const plan = data.plan;
                document.getElementById('plan-name').textContent = plan ? plan.name : '无订阅';
                
                const expireDate = data.expired_at ? new Date(data.expired_at * 1000).toLocaleDateString() : '长期有效';
                document.getElementById('plan-expire').textContent = `到期时间: ${expireDate}`;

                // --- Time Left ---
                let daysLeft = 'N/A';
                if (data.expired_at) {
                    const now = new Date();
                    const expire = new Date(data.expired_at * 1000);
                    const diffTime = expire - now;
                    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
                    daysLeft = diffDays > 0 ? `${diffDays} 天` : '已过期';
                } else {
                    daysLeft = '∞';
                }
                document.getElementById('days-left').textContent = daysLeft;
                
                // --- Reset Day ---
                if (plan && plan.reset_traffic_method === null) { // As per sample data, null seems to be monthly. Adapt if needed.
                    document.getElementById('reset-day').textContent = `每月 ${data.reset_day} 日`;
                    document.getElementById('reset-day-info').textContent = '每月流量重置日';
                } else {
                     document.getElementById('reset-day').textContent = 'N/A';
                     document.getElementById('reset-day-info').textContent = '非按月重置套餐';
                }

                // --- Traffic Usage ---
                const totalTraffic = data.transfer_enable;
                const usedTraffic = data.d + data.u;
                const remainingTraffic = totalTraffic - usedTraffic;
                const percentageUsed = totalTraffic > 0 ? Math.min((usedTraffic / totalTraffic * 100), 100).toFixed(2) : 0;

                document.getElementById('traffic-used').textContent = formatBytes(usedTraffic);
                document.getElementById('traffic-total').textContent = formatBytes(totalTraffic);
                document.getElementById('traffic-info').textContent = `剩余流量: ${formatBytes(remainingTraffic)}`;
                
                const progressBar = document.getElementById('traffic-progress');
                progressBar.style.width = `${percentageUsed}%`;
                progressBar.textContent = `${percentageUsed}%`;

                // --- Subscription Link ---
                const subLinkInput = document.getElementById('sub-link-input');
                subLinkInput.value = data.subscribe_url || '';
                
                document.getElementById('copy-sub-link-btn').addEventListener('click', () => {
                    copyToClipboard(subLinkInput.value, '订阅链接已复制!');
                });
            }

            /**
             * Formats bytes into KB, MB, GB, etc.
             * @param {number} bytes - The number of bytes.
             * @returns {string} The formatted string.
             */
            function formatBytes(bytes, decimals = 2) {
                if (bytes === 0) return '0 Bytes';
                const k = 1024;
                const dm = decimals < 0 ? 0 : decimals;
                const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB', 'PB'];
                const i = Math.floor(Math.log(bytes) / Math.log(k));
                return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];
            }

            /**
             * Copies text to the clipboard and shows a toast message.
             * @param {string} text - The text to copy.
             * @param {string} message - The success message to show.
             */
            function copyToClipboard(text, message) {
                 if (!text) {
                    showToast('没有可复制的内容', 'error');
                    return;
                }
                const textArea = document.createElement('textarea');
                textArea.value = text;
                document.body.appendChild(textArea);
                textArea.select();
                try {
                    document.execCommand('copy');
                    showToast(message, 'success');
                } catch (err) {
                    showToast('复制失败', 'error');
                }
                document.body.removeChild(textArea);
            }
            
            /**
             * Shows a toast notification.
             * @param {string} message - The message to display.
             * @param {'success'|'error'} type - The type of toast.
             */
            function showToast(message, type = 'success') {
                const toast = document.getElementById('toast-container');
                toast.textContent = message;
                toast.className = `toast ${type}`; // reset classes
                
                // Animate in
                setTimeout(() => {
                    toast.classList.add('show');
                }, 10);

                // Hide after 3 seconds
                setTimeout(() => {
                    toast.classList.remove('show');
                }, 3000);
            }
        });
    </script>
</body>
</html>
