{"name": "@heroui/accordion", "version": "2.2.19", "description": "Collapse display a list of high-level options that can expand/collapse to reveal more information.", "keywords": ["react", "accordion", "collapse", "display", "list", "expand", "tree view"], "author": "HeroUI <<EMAIL>>", "homepage": "https://heroui.com", "license": "MIT", "main": "dist/index.js", "sideEffects": false, "files": ["dist"], "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "git+https://github.com/heroui-inc/heroui.git", "directory": "packages/components/accordion"}, "bugs": {"url": "https://github.com/heroui-inc/heroui/issues"}, "peerDependencies": {"react": ">=18 || >=19.0.0-rc.0", "react-dom": ">=18 || >=19.0.0-rc.0", "framer-motion": ">=11.5.6 || >=12.0.0-alpha.1", "@heroui/theme": ">=2.4.6", "@heroui/system": ">=2.4.17"}, "dependencies": {"@react-aria/interactions": "3.25.3", "@react-aria/focus": "3.20.5", "@react-aria/utils": "3.29.1", "@react-stately/tree": "3.9.0", "@react-types/accordion": "3.0.0-alpha.26", "@react-types/shared": "3.30.0", "@heroui/aria-utils": "2.2.19", "@heroui/shared-icons": "2.1.9", "@heroui/shared-utils": "2.1.9", "@heroui/react-utils": "2.1.11", "@heroui/framer-utils": "2.1.18", "@heroui/divider": "2.2.15", "@heroui/use-aria-accordion": "2.2.14", "@heroui/dom-animation": "2.1.9"}, "clean-package": "../../../clean-package.config.json", "module": "dist/index.mjs", "types": "dist/index.d.ts", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.js"}, "./package.json": "./package.json"}, "scripts": {"build": "tsup src --dts", "build:fast": "tsup src", "dev": "pnpm build:fast --watch", "clean": "rimraf dist .turbo", "typecheck": "tsc --noEmit"}}