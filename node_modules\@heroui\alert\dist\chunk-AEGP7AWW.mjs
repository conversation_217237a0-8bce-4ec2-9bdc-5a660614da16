"use client";
import {
  useAlert
} from "./chunk-YP7VMOAN.mjs";

// src/alert.tsx
import { forwardRef } from "@heroui/system";
import {
  CloseIcon,
  DangerIcon,
  InfoCircleIcon,
  SuccessIcon,
  WarningIcon
} from "@heroui/shared-icons";
import { isEmpty } from "@heroui/shared-utils";
import { Button } from "@heroui/button";
import { cloneElement, isValidElement } from "react";
import { jsx, jsxs } from "react/jsx-runtime";
var iconMap = {
  primary: InfoCircleIcon,
  secondary: InfoCircleIcon,
  success: SuccessIcon,
  warning: WarningIcon,
  danger: DangerIcon
};
var Alert = forwardRef((props, ref) => {
  const {
    title,
    icon,
    children,
    description,
    endContent,
    startContent,
    isClosable,
    domRef,
    handleClose,
    getBaseProps,
    getMainWrapperProps,
    getDescriptionProps,
    getTitleProps,
    getCloseButtonProps,
    color,
    isVisible,
    onClose,
    getAlertIconProps,
    getIconWrapperProps
  } = useAlert({ ...props, ref });
  if (!isVisible) return null;
  const customIcon = icon && isValidElement(icon) ? cloneElement(icon, getAlertIconProps()) : null;
  const IconComponent = iconMap[color] || iconMap.primary;
  return /* @__PURE__ */ jsxs("div", { ref: domRef, role: "alert", ...getBaseProps(), children: [
    startContent,
    /* @__PURE__ */ jsx("div", { ...getIconWrapperProps(), children: customIcon || /* @__PURE__ */ jsx(IconComponent, { ...getAlertIconProps() }) }),
    /* @__PURE__ */ jsxs("div", { ...getMainWrapperProps(), children: [
      !isEmpty(title) && /* @__PURE__ */ jsx("div", { ...getTitleProps(), children: title }),
      !isEmpty(description) && /* @__PURE__ */ jsx("div", { ...getDescriptionProps(), children: description }),
      children
    ] }),
    endContent,
    (isClosable || onClose) && /* @__PURE__ */ jsx(
      Button,
      {
        isIconOnly: true,
        "aria-label": "Close",
        radius: "full",
        variant: "light",
        onPress: handleClose,
        ...getCloseButtonProps(),
        children: /* @__PURE__ */ jsx(CloseIcon, { height: 20, width: 20 })
      }
    )
  ] });
});
Alert.displayName = "HeroUI.Alert";
var alert_default = Alert;

export {
  alert_default
};
