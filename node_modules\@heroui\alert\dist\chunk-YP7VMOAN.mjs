"use client";

// src/use-alert.ts
import { mapPropsVariants } from "@heroui/system";
import { filterDOMProps, useDOMRef } from "@heroui/react-utils";
import { useCallback, useMemo } from "react";
import { mergeProps } from "@react-aria/utils";
import { alert } from "@heroui/theme";
import { useControlledState } from "@react-stately/utils";
import { clsx, dataAttr, isEmpty, objectToDeps } from "@heroui/shared-utils";
function useAlert(originalProps) {
  const [props, variantProps] = mapPropsVariants(originalProps, alert.variantKeys);
  const {
    as,
    title,
    children,
    description,
    onClose,
    isClosable,
    ref,
    icon,
    startContent,
    endContent,
    isVisible: isVisibleProp,
    isDefaultVisible,
    onVisibleChange,
    closeButtonProps = {
      size: "sm"
    },
    className,
    classNames,
    ...otherProps
  } = props;
  const [isVisible, setIsVisible] = useControlledState(
    isVisibleProp,
    isDefaultVisible != null ? isDefaultVisible : true,
    onVisibleChange
  );
  const Component = as || "div";
  const shouldFilterDOMProps = typeof Component === "string";
  const domRef = useDOMRef(ref);
  const handleClose = useCallback(() => {
    setIsVisible(false);
    onClose == null ? void 0 : onClose();
  }, [setIsVisible, onClose]);
  const baseStyles = clsx(classNames == null ? void 0 : classNames.base, className);
  const slots = useMemo(
    () => alert({ hasContent: !isEmpty(description) || !isEmpty(children), ...variantProps }),
    [description, objectToDeps(variantProps)]
  );
  const getBaseProps = useCallback(() => {
    return {
      "data-visible": dataAttr(isVisible),
      "data-closeable": dataAttr(isClosable),
      "data-has-title": dataAttr(!isEmpty(title)),
      "data-has-description": dataAttr(!isEmpty(description)),
      ...mergeProps(
        filterDOMProps(otherProps, {
          enabled: shouldFilterDOMProps
        }),
        filterDOMProps(props)
      ),
      className: slots.base({ class: baseStyles })
    };
  }, [slots, baseStyles]);
  const getMainWrapperProps = useCallback(() => {
    return {
      className: slots.mainWrapper({ class: classNames == null ? void 0 : classNames.mainWrapper })
    };
  }, [slots, classNames == null ? void 0 : classNames.mainWrapper]);
  const getDescriptionProps = useCallback(() => {
    return {
      className: slots.description({ class: classNames == null ? void 0 : classNames.description })
    };
  }, [slots, classNames == null ? void 0 : classNames.description]);
  const getTitleProps = useCallback(() => {
    return {
      className: slots.title({ class: classNames == null ? void 0 : classNames.title })
    };
  }, [slots, classNames == null ? void 0 : classNames.title]);
  const getCloseButtonProps = useCallback(
    () => ({
      ...closeButtonProps,
      className: slots.closeButton({ class: classNames == null ? void 0 : classNames.closeButton })
    }),
    [slots, classNames == null ? void 0 : classNames.closeButton]
  );
  const getAlertIconProps = useCallback(
    () => ({
      className: slots.alertIcon({ class: classNames == null ? void 0 : classNames.alertIcon })
    }),
    [slots, classNames == null ? void 0 : classNames.alertIcon]
  );
  const getIconWrapperProps = useCallback(
    () => ({
      className: slots.iconWrapper({ class: classNames == null ? void 0 : classNames.iconWrapper })
    }),
    [slots, classNames == null ? void 0 : classNames.iconWrapper]
  );
  return {
    title,
    icon,
    children,
    description,
    isClosable,
    domRef,
    endContent,
    startContent,
    getBaseProps,
    getMainWrapperProps,
    getDescriptionProps,
    getTitleProps,
    color: variantProps["color"],
    getCloseButtonProps,
    handleClose,
    isVisible,
    onClose,
    getAlertIconProps,
    getIconWrapperProps
  };
}

export {
  useAlert
};
