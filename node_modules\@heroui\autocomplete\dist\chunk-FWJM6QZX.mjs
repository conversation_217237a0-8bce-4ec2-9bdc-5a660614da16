"use client";
import {
  useAutocomplete
} from "./chunk-ENG265Z6.mjs";

// src/autocomplete.tsx
import { forwardRef } from "@heroui/system";
import { FreeSoloPopover } from "@heroui/popover";
import { ScrollShadow } from "@heroui/scroll-shadow";
import { ChevronDownIcon, CloseIcon } from "@heroui/shared-icons";
import { Listbox } from "@heroui/listbox";
import { Button } from "@heroui/button";
import { Input } from "@heroui/input";
import { AnimatePresence } from "framer-motion";
import { jsx, jsxs } from "react/jsx-runtime";
var Autocomplete = forwardRef(function Autocomplete2(props, ref) {
  var _a;
  const {
    Component,
    isOpen,
    disableAnimation,
    selectorIcon = /* @__PURE__ */ jsx(ChevronDownIcon, {}),
    clearIcon = /* @__PURE__ */ jsx(CloseIcon, {}),
    endContent,
    getBaseProps,
    getSelectorButtonProps,
    getInputProps,
    getListBoxProps,
    getPopoverProps,
    getEmptyPopoverProps,
    getClearButtonProps,
    getListBoxWrapperProps,
    getEndContentWrapperProps
  } = useAutocomplete({ ...props, ref });
  const listboxProps = getListBoxProps();
  const popoverContent = isOpen ? /* @__PURE__ */ jsx(FreeSoloPopover, { ...getPopoverProps(), children: /* @__PURE__ */ jsx(ScrollShadow, { ...getListBoxWrapperProps(), children: /* @__PURE__ */ jsx(Listbox, { ...listboxProps }) }) }) : ((_a = listboxProps.state) == null ? void 0 : _a.collection.size) === 0 ? /* @__PURE__ */ jsx("div", { ...getEmptyPopoverProps() }) : null;
  return /* @__PURE__ */ jsxs(Component, { ...getBaseProps(), children: [
    /* @__PURE__ */ jsx(
      Input,
      {
        ...getInputProps(),
        endContent: /* @__PURE__ */ jsxs("div", { ...getEndContentWrapperProps(), children: [
          endContent || /* @__PURE__ */ jsx(Button, { ...getClearButtonProps(), children: clearIcon }),
          /* @__PURE__ */ jsx(Button, { ...getSelectorButtonProps(), children: selectorIcon })
        ] })
      }
    ),
    disableAnimation ? popoverContent : /* @__PURE__ */ jsx(AnimatePresence, { children: popoverContent })
  ] });
});
var autocomplete_default = Autocomplete;

export {
  autocomplete_default
};
