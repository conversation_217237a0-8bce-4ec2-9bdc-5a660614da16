# @heroui/avatar

The Avatar component is used to represent a user, and displays the profile picture, initials or fallback icon.

Please refer to the [documentation](https://heroui.com/docs/components/avatar) for more information.

## Installation

```sh
yarn add @heroui/avatar
# or
npm i @heroui/avatar
```

## Contribution

Yes please! See the
[contributing guidelines](https://github.com/heroui-inc/heroui/blob/master/CONTRIBUTING.md)
for details.

## License

This project is licensed under the terms of the
[MIT license](https://github.com/heroui-inc/heroui/blob/master/LICENSE).
