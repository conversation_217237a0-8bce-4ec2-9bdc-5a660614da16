"use client";
"use strict";
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);

// src/use-avatar.ts
var use_avatar_exports = {};
__export(use_avatar_exports, {
  useAvatar: () => useAvatar
});
module.exports = __toCommonJS(use_avatar_exports);
var import_theme = require("@heroui/theme");
var import_system = require("@heroui/system");
var import_utils = require("@react-aria/utils");
var import_react_utils2 = require("@heroui/react-utils");
var import_shared_utils = require("@heroui/shared-utils");
var import_focus = require("@react-aria/focus");
var import_react = require("react");
var import_use_image = require("@heroui/use-image");
var import_interactions = require("@react-aria/interactions");

// src/avatar-group-context.ts
var import_react_utils = require("@heroui/react-utils");
var [AvatarGroupProvider, useAvatarGroupContext] = (0, import_react_utils.createContext)({
  name: "AvatarGroupContext",
  strict: false
});

// src/use-avatar.ts
function useAvatar(originalProps = {}) {
  var _a, _b, _c, _d, _e, _f;
  const globalContext = (0, import_system.useProviderContext)();
  const groupContext = useAvatarGroupContext();
  const isInGroup = !!groupContext;
  const {
    as,
    ref,
    src,
    name,
    icon,
    classNames,
    fallback,
    alt = name || "avatar",
    imgRef: imgRefProp,
    color = (_a = groupContext == null ? void 0 : groupContext.color) != null ? _a : "default",
    radius = (_b = groupContext == null ? void 0 : groupContext.radius) != null ? _b : "full",
    size = (_c = groupContext == null ? void 0 : groupContext.size) != null ? _c : "md",
    isBordered = (_d = groupContext == null ? void 0 : groupContext.isBordered) != null ? _d : false,
    isDisabled = (_e = groupContext == null ? void 0 : groupContext.isDisabled) != null ? _e : false,
    isFocusable = false,
    getInitials = import_shared_utils.safeText,
    ignoreFallback = false,
    showFallback: showFallbackProp = false,
    ImgComponent = "img",
    imgProps,
    className,
    onError,
    disableAnimation: disableAnimationProp,
    ...otherProps
  } = originalProps;
  const Component = as || "span";
  const domRef = (0, import_react_utils2.useDOMRef)(ref);
  const imgRef = (0, import_react_utils2.useDOMRef)(imgRefProp);
  const { isFocusVisible, isFocused, focusProps } = (0, import_focus.useFocusRing)();
  const { isHovered, hoverProps } = (0, import_interactions.useHover)({ isDisabled });
  const disableAnimation = (_f = disableAnimationProp != null ? disableAnimationProp : globalContext == null ? void 0 : globalContext.disableAnimation) != null ? _f : false;
  const imageStatus = (0, import_use_image.useImage)({ src, onError, ignoreFallback });
  const isImgLoaded = imageStatus === "loaded";
  const shouldFilterDOMProps = typeof ImgComponent === "string";
  const showFallback = (!src || !isImgLoaded) && showFallbackProp;
  const slots = (0, import_react.useMemo)(
    () => {
      var _a2;
      return (0, import_theme.avatar)({
        color,
        radius,
        size,
        isBordered,
        isDisabled,
        isInGroup,
        disableAnimation,
        isInGridGroup: (_a2 = groupContext == null ? void 0 : groupContext.isGrid) != null ? _a2 : false
      });
    },
    [
      color,
      radius,
      size,
      isBordered,
      isDisabled,
      disableAnimation,
      isInGroup,
      groupContext == null ? void 0 : groupContext.isGrid
    ]
  );
  const baseStyles = (0, import_shared_utils.clsx)(classNames == null ? void 0 : classNames.base, className);
  const canBeFocused = (0, import_react.useMemo)(() => {
    return isFocusable || as === "button";
  }, [isFocusable, as]);
  const getAvatarProps = (0, import_react.useCallback)(
    (props = {}) => ({
      ref: domRef,
      tabIndex: canBeFocused ? 0 : -1,
      "data-hover": (0, import_shared_utils.dataAttr)(isHovered),
      "data-focus": (0, import_shared_utils.dataAttr)(isFocused),
      "data-focus-visible": (0, import_shared_utils.dataAttr)(isFocusVisible),
      className: slots.base({
        class: (0, import_shared_utils.clsx)(baseStyles, props == null ? void 0 : props.className)
      }),
      ...(0, import_utils.mergeProps)(otherProps, hoverProps, canBeFocused ? focusProps : {})
    }),
    [canBeFocused, slots, baseStyles, focusProps, otherProps]
  );
  const getImageProps = (0, import_react.useCallback)(
    (props = {}) => ({
      ref: imgRef,
      src,
      "data-loaded": (0, import_shared_utils.dataAttr)(isImgLoaded),
      className: slots.img({ class: classNames == null ? void 0 : classNames.img }),
      ...(0, import_utils.mergeProps)(
        imgProps,
        props,
        (0, import_react_utils2.filterDOMProps)({ disableAnimation }, {
          enabled: shouldFilterDOMProps
        })
      )
    }),
    [slots, isImgLoaded, imgProps, disableAnimation, src, imgRef, shouldFilterDOMProps]
  );
  return {
    Component,
    ImgComponent,
    src,
    alt,
    icon,
    name,
    imgRef,
    slots,
    classNames,
    fallback,
    isImgLoaded,
    showFallback,
    ignoreFallback,
    getInitials,
    getAvatarProps,
    getImageProps
  };
}
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
  useAvatar
});
