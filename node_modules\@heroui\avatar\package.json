{"name": "@heroui/avatar", "version": "2.2.18", "description": "The Avatar component is used to represent a user, and displays the profile picture, initials or fallback icon.", "keywords": ["avatar"], "author": "HeroUI <<EMAIL>>", "homepage": "https://heroui.com", "license": "MIT", "main": "dist/index.js", "sideEffects": false, "files": ["dist"], "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "git+https://github.com/heroui-inc/heroui.git", "directory": "packages/components/avatar"}, "bugs": {"url": "https://github.com/heroui-inc/heroui/issues"}, "peerDependencies": {"react": ">=18 || >=19.0.0-rc.0", "react-dom": ">=18 || >=19.0.0-rc.0", "@heroui/theme": ">=2.4.6", "@heroui/system": ">=2.4.17"}, "dependencies": {"@react-aria/interactions": "3.25.3", "@react-aria/focus": "3.20.5", "@react-aria/utils": "3.29.1", "@heroui/shared-utils": "2.1.9", "@heroui/react-utils": "2.1.11", "@heroui/use-image": "2.1.10"}, "clean-package": "../../../clean-package.config.json", "module": "dist/index.mjs", "types": "dist/index.d.ts", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.js"}, "./package.json": "./package.json"}, "scripts": {"build": "tsup src --dts", "dev": "pnpm build:fast --watch", "clean": "rimraf dist .turbo", "typecheck": "tsc --noEmit", "build:fast": "tsup src"}}