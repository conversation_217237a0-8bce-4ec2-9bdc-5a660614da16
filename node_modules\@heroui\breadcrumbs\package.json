{"name": "@heroui/breadcrumbs", "version": "2.2.18", "description": "Breadcrumbs display a hierarchy of links to the current page or resource in an application.", "keywords": ["breadcrumbs"], "author": "HeroUI <<EMAIL>>", "homepage": "https://heroui.com", "license": "MIT", "main": "dist/index.js", "sideEffects": false, "files": ["dist"], "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "git+https://github.com/heroui-inc/heroui.git", "directory": "packages/components/breadcrumbs"}, "bugs": {"url": "https://github.com/heroui-inc/heroui/issues"}, "peerDependencies": {"react": ">=18 || >=19.0.0-rc.0", "react-dom": ">=18 || >=19.0.0-rc.0", "@heroui/theme": ">=2.4.6", "@heroui/system": ">=2.4.17"}, "dependencies": {"@react-aria/focus": "3.20.5", "@react-aria/breadcrumbs": "3.5.26", "@react-aria/utils": "3.29.1", "@react-types/breadcrumbs": "3.7.14", "@heroui/react-utils": "2.1.11", "@heroui/shared-utils": "2.1.9", "@heroui/shared-icons": "2.1.9"}, "clean-package": "../../../clean-package.config.json", "module": "dist/index.mjs", "types": "dist/index.d.ts", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.js"}, "./package.json": "./package.json"}, "scripts": {"build": "tsup src --dts", "build:fast": "tsup src", "dev": "pnpm build:fast --watch", "clean": "rimraf dist .turbo", "typecheck": "tsc --noEmit"}}