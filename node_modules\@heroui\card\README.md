# @heroui/card

Card is a container for text, photos, and actions in the context of a single subject.

Please refer to the [documentation](https://heroui.com/docs/components/card) for more information.

## Installation

```sh
yarn add @heroui/card
# or
npm i @heroui/card
```

## Contribution

Yes please! See the
[contributing guidelines](https://github.com/heroui-inc/heroui/blob/master/CONTRIBUTING.md)
for details.

## License

This project is licensed under the terms of the
[MIT license](https://github.com/heroui-inc/heroui/blob/master/LICENSE).
