# @heroui/checkbox

Checkboxes allow users to select multiple items from a list of individual items, or to mark one individual item as selected.

Please refer to the [documentation](https://heroui.com/docs/components/checkbox) for more information.

## Installation

```sh
yarn add @heroui/checkbox
# or
npm i @heroui/checkbox
```

## Contribution

Yes please! See the
[contributing guidelines](https://github.com/heroui-inc/heroui/blob/master/CONTRIBUTING.md)
for details.

## License

This project is licensed under the terms of the
[MIT license](https://github.com/heroui-inc/heroui/blob/master/LICENSE).
