# @heroui/input-otp

InputOTP is a component that allows users to enter otp input. It can be used to get user otp in forms.

This package contains the InputOTPcomponent.

Please refer to the [documentation](https://heroui.com/docs/components/input-otp) for more information.

## Installation

```sh
yarn add @heroui/input-otp
# or
npm i @heroui/input-otp
```

## Contribution

Yes please! See the
[contributing guidelines](https://github.com/heroui-inc/heroui/blob/master/CONTRIBUTING.md)
for details.

## License

This project is licensed under the terms of the
[MIT license](https://github.com/heroui-inc/heroui/blob/master/LICENSE).
