"use client";
import {
  useInputOtpContext
} from "./chunk-6IRH5EFF.mjs";

// src/input-otp-segment.tsx
import { useMemo } from "react";
import { clsx, dataAttr } from "@heroui/shared-utils";
import { cn } from "@heroui/theme";
import { jsx } from "react/jsx-runtime";
var InputOtpSegment = ({
  ...props
}) => {
  var _a;
  const { classNames, slots, type } = useInputOtpContext();
  const passwordCharStyles = clsx(classNames == null ? void 0 : classNames.passwordChar);
  const caretStyles = clsx(classNames == null ? void 0 : classNames.caret);
  const segmentStyles = clsx(classNames == null ? void 0 : classNames.segment);
  const displayValue = useMemo(() => {
    var _a2, _b;
    if (props.isActive && !props.char) {
      return /* @__PURE__ */ jsx("div", { className: cn((_a2 = slots.caret) == null ? void 0 : _a2.call(slots, { class: caretStyles })) });
    }
    if (props.char) {
      return type === "password" ? /* @__PURE__ */ jsx("div", { className: cn((_b = slots.passwordChar) == null ? void 0 : _b.call(slots, { class: passwordCharStyles })) }) : /* @__PURE__ */ jsx("div", { children: props.char });
    }
    return /* @__PURE__ */ jsx("div", { children: props.placeholderChar });
  }, [props.char, props.isActive, props.placeholderChar, type]);
  return /* @__PURE__ */ jsx(
    "div",
    {
      className: cn((_a = slots.segment) == null ? void 0 : _a.call(slots, { class: segmentStyles })),
      "data-active": dataAttr(props.isActive),
      "data-focus": dataAttr(props.isFocused && props.isActive),
      "data-focus-visible": dataAttr(props.isFocusVisible && props.isActive),
      "data-has-value": dataAttr(!!props.char),
      "data-slot": "segment2",
      role: "presentation",
      children: displayValue
    }
  );
};

export {
  InputOtpSegment
};
