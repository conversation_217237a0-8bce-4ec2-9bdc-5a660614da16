"use client";
import {
  InputOtpSegment
} from "./chunk-2357QUJK.mjs";
import {
  InputOtpProvider
} from "./chunk-6IRH5EFF.mjs";
import {
  useInputOtp
} from "./chunk-YUTBZOPC.mjs";

// src/input-otp.tsx
import { forwardRef } from "@heroui/system";
import { useMemo } from "react";
import { OTPInput } from "input-otp";
import { jsx, jsxs } from "react/jsx-runtime";
var InputOtp = forwardRef((props, ref) => {
  const context = useInputOtp({ ...props, ref });
  const {
    Component,
    hasHelper,
    isInvalid,
    errorMessage,
    description,
    isFocusVisible,
    isFocused,
    getBaseProps,
    getInputOtpProps,
    getSegmentWrapperProps,
    getHelperWrapperProps,
    getErrorMessageProps,
    getDescriptionProps
  } = context;
  const helperSection = useMemo(() => {
    if (!hasHelper) {
      return null;
    }
    return /* @__PURE__ */ jsx("div", { ...getHelperWrapperProps(), children: isInvalid && errorMessage ? /* @__PURE__ */ jsx("div", { ...getErrorMessageProps(), children: errorMessage }) : /* @__PURE__ */ jsx("div", { ...getDescriptionProps(), children: description }) });
  }, [
    hasHelper,
    isInvalid,
    errorMessage,
    description,
    getHelperWrapperProps,
    getErrorMessageProps,
    getDescriptionProps
  ]);
  return /* @__PURE__ */ jsx(InputOtpProvider, { value: context, children: /* @__PURE__ */ jsxs(Component, { ...getBaseProps(), children: [
    /* @__PURE__ */ jsx(
      OTPInput,
      {
        ...getInputOtpProps(),
        render: ({ slots }) => /* @__PURE__ */ jsx("div", { ...getSegmentWrapperProps(), children: slots.map((slot, idx) => /* @__PURE__ */ jsx(
          InputOtpSegment,
          {
            ...slot,
            isFocusVisible,
            isFocused
          },
          idx
        )) })
      }
    ),
    helperSection
  ] }) });
});
InputOtp.displayName = "HeroUI.InputOtp";
var input_otp_default = InputOtp;

export {
  input_otp_default
};
