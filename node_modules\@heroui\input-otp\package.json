{"name": "@heroui/input-otp", "version": "2.1.21", "description": "", "keywords": ["input-otp"], "author": "HeroUI <<EMAIL>>", "homepage": "https://heroui.com", "license": "MIT", "main": "dist/index.js", "sideEffects": false, "files": ["dist"], "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "git+https://github.com/heroui-inc/heroui.git", "directory": "packages/components/input-otp"}, "bugs": {"url": "https://github.com/heroui-inc/heroui/issues"}, "peerDependencies": {"react": ">=18", "react-dom": ">=18", "@heroui/theme": ">=2.4.16", "@heroui/system": ">=2.4.17"}, "dependencies": {"@react-aria/utils": "3.29.1", "@react-aria/form": "3.0.18", "@react-stately/utils": "3.10.7", "@react-stately/form": "3.1.5", "@react-types/textfield": "3.12.3", "input-otp": "1.4.1", "@react-aria/focus": "3.20.5", "@heroui/form": "2.1.21", "@heroui/shared-utils": "2.1.9", "@heroui/react-utils": "2.1.11"}, "clean-package": "../../../clean-package.config.json", "module": "dist/index.mjs", "types": "dist/index.d.ts", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.js"}, "./package.json": "./package.json"}, "scripts": {"build": "tsup src --dts", "build:fast": "tsup src", "dev": "pnpm build:fast --watch", "clean": "rimraf dist .turbo", "typecheck": "tsc --noEmit"}}