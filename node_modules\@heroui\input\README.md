# @heroui/input

Input is a component that allows users to enter text. It can be used to get user inputs in forms, search fields, and more.

This package contains the Input component and the TextArea component.

Please refer to the [documentation](https://heroui.com/docs/components/input) for more information.

## Installation

```sh
yarn add @heroui/input
# or
npm i @heroui/input
```

## Contribution

Yes please! See the
[contributing guidelines](https://github.com/heroui-inc/heroui/blob/master/CONTRIBUTING.md)
for details.

## License

This project is licensed under the terms of the
[MIT license](https://github.com/heroui-inc/heroui/blob/master/LICENSE).
