# @heroui/link

Links allow users to click their way from page to page. This component is styled to resemble a hyperlink and semantically renders an `<a>` tag.

Please refer to the [documentation](https://heroui.com/docs/components/link) for more information.

## Installation

```sh
yarn add @heroui/link
# or
npm i @heroui/link
```

## Contribution

Yes please! See the
[contributing guidelines](https://github.com/heroui-inc/heroui/blob/master/CONTRIBUTING.md)
for details.

## License

This project is licensed under the terms of the
[MIT license](https://github.com/heroui-inc/heroui/blob/master/LICENSE).
