"use client";

// src/use-link.ts
import { link } from "@heroui/theme";
import { useAriaLink } from "@heroui/use-aria-link";
import { mapPropsVariants, useProviderContext } from "@heroui/system";
import { useDOMRef } from "@heroui/react-utils";
import { useFocusRing } from "@react-aria/focus";
import { dataAttr, objectToDeps } from "@heroui/shared-utils";
import { useMemo, useCallback } from "react";
import { mergeProps } from "@react-aria/utils";
function useLink(originalProps) {
  var _a, _b, _c, _d;
  const globalContext = useProviderContext();
  const [props, variantProps] = mapPropsVariants(originalProps, link.variantKeys);
  const {
    ref,
    as,
    children,
    anchorIcon,
    isExternal = false,
    showAnchorIcon = false,
    autoFocus = false,
    className,
    onPress,
    onPressStart,
    onPressEnd,
    onClick,
    ...otherProps
  } = props;
  const Component = as || "a";
  const domRef = useDOMRef(ref);
  const disableAnimation = (_b = (_a = originalProps == null ? void 0 : originalProps.disableAnimation) != null ? _a : globalContext == null ? void 0 : globalContext.disableAnimation) != null ? _b : false;
  const { linkProps } = useAriaLink(
    {
      ...otherProps,
      onPress,
      onPressStart,
      onPressEnd,
      // @ts-ignore React Aria Link does accept onClick as a prop but it's not in the types
      onClick,
      isDisabled: originalProps.isDisabled,
      elementType: `${as}`
    },
    domRef
  );
  const { isFocused, isFocusVisible, focusProps } = useFocusRing({
    autoFocus
  });
  if (isExternal) {
    otherProps.rel = (_c = otherProps.rel) != null ? _c : "noopener noreferrer";
    otherProps.target = (_d = otherProps.target) != null ? _d : "_blank";
  }
  const styles = useMemo(
    () => link({
      ...variantProps,
      disableAnimation,
      className
    }),
    [objectToDeps(variantProps), disableAnimation, className]
  );
  const getLinkProps = useCallback(() => {
    return {
      ref: domRef,
      className: styles,
      "data-focus": dataAttr(isFocused),
      "data-disabled": dataAttr(originalProps.isDisabled),
      "data-focus-visible": dataAttr(isFocusVisible),
      ...mergeProps(focusProps, linkProps, otherProps)
    };
  }, [styles, isFocused, isFocusVisible, focusProps, linkProps, otherProps]);
  return { Component, children, anchorIcon, showAnchorIcon, getLinkProps };
}

export {
  useLink
};
