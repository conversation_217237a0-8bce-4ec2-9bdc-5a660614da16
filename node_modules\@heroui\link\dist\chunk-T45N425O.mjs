"use client";
import {
  useLink
} from "./chunk-SGLWUJCW.mjs";

// src/link.tsx
import { forwardRef } from "@heroui/system";
import { LinkIcon } from "@heroui/shared-icons";
import { linkAnchorClasses } from "@heroui/theme";
import { Fragment, jsx, jsxs } from "react/jsx-runtime";
var Link = forwardRef((props, ref) => {
  const {
    Component,
    children,
    showAnchorIcon,
    anchorIcon = /* @__PURE__ */ jsx(LinkIcon, { className: linkAnchorClasses }),
    getLinkProps
  } = useLink({
    ref,
    ...props
  });
  return /* @__PURE__ */ jsx(Component, { ...getLinkProps(), children: /* @__PURE__ */ jsxs(Fragment, { children: [
    children,
    showAnchorIcon && anchorIcon
  ] }) });
});
Link.displayName = "HeroUI.Link";
var link_default = Link;

export {
  link_default
};
