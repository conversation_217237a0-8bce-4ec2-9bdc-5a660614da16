"use client";
"use strict";
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);

// src/use-listbox-item.ts
var use_listbox_item_exports = {};
__export(use_listbox_item_exports, {
  useListboxItem: () => useListboxItem
});
module.exports = __toCommonJS(use_listbox_item_exports);
var import_react = require("react");
var import_theme = require("@heroui/theme");
var import_system = require("@heroui/system");
var import_focus = require("@react-aria/focus");
var import_react_utils = require("@heroui/react-utils");
var import_shared_utils = require("@heroui/shared-utils");
var import_listbox = require("@react-aria/listbox");
var import_utils = require("@react-aria/utils");
var import_interactions = require("@react-aria/interactions");
var import_use_is_mobile = require("@heroui/use-is-mobile");
function useListboxItem(originalProps) {
  var _a, _b;
  const globalContext = (0, import_system.useProviderContext)();
  const [props, variantProps] = (0, import_system.mapPropsVariants)(originalProps, import_theme.listboxItem.variantKeys);
  const {
    as,
    item,
    state,
    description,
    startContent,
    endContent,
    isVirtualized,
    selectedIcon,
    className,
    classNames,
    autoFocus,
    onPress,
    onPressUp,
    onPressStart,
    onPressEnd,
    onPressChange,
    onClick,
    shouldHighlightOnFocus,
    hideSelectedIcon = false,
    isReadOnly = false,
    ...otherProps
  } = props;
  const disableAnimation = (_b = (_a = originalProps.disableAnimation) != null ? _a : globalContext == null ? void 0 : globalContext.disableAnimation) != null ? _b : false;
  const domRef = (0, import_react.useRef)(null);
  const Component = as || (originalProps.href ? "a" : "li");
  const shouldFilterDOMProps = typeof Component === "string";
  const { rendered, key } = item;
  const isDisabled = state.disabledKeys.has(key) || originalProps.isDisabled;
  const isSelectable = state.selectionManager.selectionMode !== "none";
  const isMobile = (0, import_use_is_mobile.useIsMobile)();
  const { pressProps, isPressed } = (0, import_interactions.usePress)({
    ref: domRef,
    isDisabled,
    onClick,
    onPress,
    onPressUp,
    onPressStart,
    onPressEnd,
    onPressChange
  });
  const { isHovered, hoverProps } = (0, import_interactions.useHover)({
    isDisabled
  });
  const { isFocusVisible, focusProps } = (0, import_focus.useFocusRing)({
    autoFocus
  });
  const { isFocused, isSelected, optionProps, labelProps, descriptionProps } = (0, import_listbox.useOption)(
    {
      key,
      isDisabled,
      "aria-label": props["aria-label"],
      isVirtualized
    },
    state,
    domRef
  );
  let itemProps = optionProps;
  const slots = (0, import_react.useMemo)(
    () => (0, import_theme.listboxItem)({
      ...variantProps,
      isDisabled,
      disableAnimation,
      hasTitleTextChild: typeof rendered === "string",
      hasDescriptionTextChild: typeof description === "string"
    }),
    [(0, import_shared_utils.objectToDeps)(variantProps), isDisabled, disableAnimation, rendered, description]
  );
  const baseStyles = (0, import_shared_utils.clsx)(classNames == null ? void 0 : classNames.base, className);
  if (isReadOnly) {
    itemProps = (0, import_shared_utils.removeEvents)(itemProps);
  }
  const isHighlighted = shouldHighlightOnFocus && isFocused || (isMobile ? isHovered || isPressed : isHovered || isFocused && !isFocusVisible);
  const getItemProps = (props2 = {}) => ({
    ref: domRef,
    ...(0, import_utils.mergeProps)(
      itemProps,
      isReadOnly ? {} : (0, import_utils.mergeProps)(focusProps, pressProps),
      hoverProps,
      (0, import_react_utils.filterDOMProps)(otherProps, {
        enabled: shouldFilterDOMProps
      }),
      props2
    ),
    "data-selectable": (0, import_shared_utils.dataAttr)(isSelectable),
    "data-focus": (0, import_shared_utils.dataAttr)(isFocused),
    "data-hover": (0, import_shared_utils.dataAttr)(isHighlighted),
    "data-disabled": (0, import_shared_utils.dataAttr)(isDisabled),
    "data-selected": (0, import_shared_utils.dataAttr)(isSelected),
    "data-pressed": (0, import_shared_utils.dataAttr)(isPressed),
    "data-focus-visible": (0, import_shared_utils.dataAttr)(isFocusVisible),
    className: slots.base({ class: (0, import_shared_utils.clsx)(baseStyles, props2.className) })
  });
  const getLabelProps = (props2 = {}) => ({
    ...(0, import_utils.mergeProps)(labelProps, props2),
    "data-label": (0, import_shared_utils.dataAttr)(true),
    className: slots.title({ class: classNames == null ? void 0 : classNames.title })
  });
  const getDescriptionProps = (props2 = {}) => ({
    ...(0, import_utils.mergeProps)(descriptionProps, props2),
    className: slots.description({ class: classNames == null ? void 0 : classNames.description })
  });
  const getWrapperProps = (props2 = {}) => ({
    ...(0, import_utils.mergeProps)(props2),
    className: slots.wrapper({ class: classNames == null ? void 0 : classNames.wrapper })
  });
  const getSelectedIconProps = (0, import_react.useCallback)(
    (props2 = {}) => {
      return {
        "aria-hidden": (0, import_shared_utils.dataAttr)(true),
        "data-disabled": (0, import_shared_utils.dataAttr)(isDisabled),
        className: slots.selectedIcon({ class: classNames == null ? void 0 : classNames.selectedIcon }),
        ...props2
      };
    },
    [isDisabled, slots, classNames]
  );
  return {
    Component,
    domRef,
    slots,
    classNames,
    isSelectable,
    isSelected,
    isDisabled,
    rendered,
    description,
    startContent,
    endContent,
    selectedIcon,
    hideSelectedIcon,
    disableAnimation,
    getItemProps,
    getLabelProps,
    getWrapperProps,
    getDescriptionProps,
    getSelectedIconProps
  };
}
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
  useListboxItem
});
