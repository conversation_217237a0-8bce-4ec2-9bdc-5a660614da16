"use client";
"use strict";
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);

// src/menu.tsx
var menu_exports = {};
__export(menu_exports, {
  default: () => menu_default
});
module.exports = __toCommonJS(menu_exports);
var import_system4 = require("@heroui/system");
var import_theme4 = require("@heroui/theme");

// src/use-menu.ts
var import_system = require("@heroui/system");
var import_menu = require("@react-aria/menu");
var import_theme = require("@heroui/theme");
var import_tree = require("@react-stately/tree");
var import_react_utils = require("@heroui/react-utils");
var import_react = require("react");
var import_shared_utils = require("@heroui/shared-utils");
function useMenu(props) {
  var _a;
  const globalContext = (0, import_system.useProviderContext)();
  const {
    as,
    ref,
    variant,
    color,
    children,
    disableAnimation = (_a = globalContext == null ? void 0 : globalContext.disableAnimation) != null ? _a : false,
    onAction,
    closeOnSelect,
    itemClasses,
    className,
    state: propState,
    topContent,
    bottomContent,
    hideEmptyContent = false,
    hideSelectedIcon = false,
    emptyContent = "No items.",
    menuProps: userMenuProps,
    onClose,
    classNames,
    ...otherProps
  } = props;
  const Component = as || "ul";
  const domRef = (0, import_react_utils.useDOMRef)(ref);
  const shouldFilterDOMProps = typeof Component === "string";
  const innerState = (0, import_tree.useTreeState)({ ...otherProps, ...userMenuProps, children });
  const state = propState || innerState;
  const { menuProps } = (0, import_menu.useMenu)({ ...otherProps, ...userMenuProps, onAction }, state, domRef);
  const slots = (0, import_react.useMemo)(() => (0, import_theme.menu)({ className }), [className]);
  const baseStyles = (0, import_shared_utils.clsx)(classNames == null ? void 0 : classNames.base, className);
  const getBaseProps = (props2 = {}) => {
    return {
      ref: domRef,
      "data-slot": "base",
      className: slots.base({ class: baseStyles }),
      ...(0, import_react_utils.filterDOMProps)(otherProps, {
        enabled: shouldFilterDOMProps
      }),
      ...props2
    };
  };
  const getListProps = (props2 = {}) => {
    return {
      "data-slot": "list",
      className: slots.list({ class: classNames == null ? void 0 : classNames.list }),
      ...menuProps,
      ...props2
    };
  };
  const getEmptyContentProps = (props2 = {}) => {
    return {
      children: emptyContent,
      className: slots.emptyContent({ class: classNames == null ? void 0 : classNames.emptyContent }),
      ...props2
    };
  };
  return {
    Component,
    state,
    variant,
    color,
    disableAnimation,
    onClose,
    topContent,
    bottomContent,
    closeOnSelect,
    className,
    itemClasses,
    getBaseProps,
    getListProps,
    hideEmptyContent,
    hideSelectedIcon,
    getEmptyContentProps
  };
}

// src/menu-section.tsx
var import_theme3 = require("@heroui/theme");
var import_menu3 = require("@react-aria/menu");
var import_react4 = require("react");
var import_system3 = require("@heroui/system");
var import_utils2 = require("@react-aria/utils");
var import_shared_utils3 = require("@heroui/shared-utils");
var import_divider = require("@heroui/divider");

// src/menu-item.tsx
var import_react3 = require("react");

// src/use-menu-item.ts
var import_react2 = require("react");
var import_theme2 = require("@heroui/theme");
var import_system2 = require("@heroui/system");
var import_focus = require("@react-aria/focus");
var import_shared_utils2 = require("@heroui/shared-utils");
var import_menu2 = require("@react-aria/menu");
var import_interactions = require("@react-aria/interactions");
var import_utils = require("@react-aria/utils");
var import_use_is_mobile = require("@heroui/use-is-mobile");
var import_react_utils2 = require("@heroui/react-utils");
function useMenuItem(originalProps) {
  var _a, _b;
  const globalContext = (0, import_system2.useProviderContext)();
  const [props, variantProps] = (0, import_system2.mapPropsVariants)(originalProps, import_theme2.menuItem.variantKeys);
  const {
    as,
    item,
    state,
    shortcut,
    description,
    startContent,
    endContent,
    isVirtualized,
    selectedIcon,
    className,
    classNames,
    onAction,
    autoFocus,
    onPress,
    onPressStart,
    onPressUp,
    onPressEnd,
    onPressChange,
    onHoverStart: hoverStartProp,
    onHoverChange,
    onHoverEnd,
    hideSelectedIcon = false,
    isReadOnly = false,
    closeOnSelect,
    onClose,
    onClick,
    ...otherProps
  } = props;
  const disableAnimation = (_b = (_a = originalProps.disableAnimation) != null ? _a : globalContext == null ? void 0 : globalContext.disableAnimation) != null ? _b : false;
  const domRef = (0, import_react2.useRef)(null);
  const Component = as || ((otherProps == null ? void 0 : otherProps.href) ? "a" : "li");
  const shouldFilterDOMProps = typeof Component === "string";
  const { rendered, key } = item;
  const isDisabledProp = state.disabledKeys.has(key) || originalProps.isDisabled;
  const isSelectable = state.selectionManager.selectionMode !== "none";
  const isMobile = (0, import_use_is_mobile.useIsMobile)();
  const { isFocusVisible, focusProps } = (0, import_focus.useFocusRing)({
    autoFocus
  });
  const handlePress = (0, import_react2.useCallback)(
    (e) => {
      onClick == null ? void 0 : onClick(e);
      onPress == null ? void 0 : onPress(e);
    },
    [onClick, onPress]
  );
  const {
    isPressed,
    isFocused,
    isSelected,
    isDisabled,
    menuItemProps,
    labelProps,
    descriptionProps,
    keyboardShortcutProps
  } = (0, import_menu2.useMenuItem)(
    {
      key,
      onClose,
      isDisabled: isDisabledProp,
      onPress: handlePress,
      onPressStart,
      onPressUp,
      onPressEnd,
      onPressChange,
      "aria-label": props["aria-label"],
      closeOnSelect,
      isVirtualized,
      onAction
    },
    state,
    domRef
  );
  let { hoverProps, isHovered } = (0, import_interactions.useHover)({
    isDisabled,
    onHoverStart(e) {
      if (!(0, import_interactions.isFocusVisible)()) {
        state.selectionManager.setFocused(true);
        state.selectionManager.setFocusedKey(key);
      }
      hoverStartProp == null ? void 0 : hoverStartProp(e);
    },
    onHoverChange,
    onHoverEnd
  });
  let itemProps = menuItemProps;
  const slots = (0, import_react2.useMemo)(
    () => (0, import_theme2.menuItem)({
      ...variantProps,
      isDisabled,
      disableAnimation,
      hasTitleTextChild: typeof rendered === "string",
      hasDescriptionTextChild: typeof description === "string"
    }),
    [(0, import_shared_utils2.objectToDeps)(variantProps), isDisabled, disableAnimation, rendered, description]
  );
  const baseStyles = (0, import_shared_utils2.clsx)(classNames == null ? void 0 : classNames.base, className);
  if (isReadOnly) {
    itemProps = (0, import_shared_utils2.removeEvents)(itemProps);
  }
  const getItemProps = (props2 = {}) => ({
    ref: domRef,
    ...(0, import_utils.mergeProps)(
      isReadOnly ? {} : focusProps,
      (0, import_react_utils2.filterDOMProps)(otherProps, {
        enabled: shouldFilterDOMProps
      }),
      itemProps,
      hoverProps,
      props2
    ),
    "data-focus": (0, import_shared_utils2.dataAttr)(isFocused),
    "data-selectable": (0, import_shared_utils2.dataAttr)(isSelectable),
    "data-hover": (0, import_shared_utils2.dataAttr)(isMobile ? isHovered || isPressed : isHovered),
    "data-disabled": (0, import_shared_utils2.dataAttr)(isDisabled),
    "data-selected": (0, import_shared_utils2.dataAttr)(isSelected),
    "data-pressed": (0, import_shared_utils2.dataAttr)(isPressed),
    "data-focus-visible": (0, import_shared_utils2.dataAttr)(isFocusVisible),
    className: slots.base({ class: (0, import_shared_utils2.clsx)(baseStyles, props2.className) })
  });
  const getLabelProps = (props2 = {}) => ({
    ...(0, import_utils.mergeProps)(labelProps, props2),
    className: slots.title({ class: classNames == null ? void 0 : classNames.title })
  });
  const getDescriptionProps = (props2 = {}) => ({
    ...(0, import_utils.mergeProps)(descriptionProps, props2),
    className: slots.description({ class: classNames == null ? void 0 : classNames.description })
  });
  const getKeyboardShortcutProps = (props2 = {}) => ({
    ...(0, import_utils.mergeProps)(keyboardShortcutProps, props2),
    className: slots.shortcut({ class: classNames == null ? void 0 : classNames.shortcut })
  });
  const getSelectedIconProps = (0, import_react2.useCallback)(
    (props2 = {}) => {
      return {
        "aria-hidden": (0, import_shared_utils2.dataAttr)(true),
        "data-disabled": (0, import_shared_utils2.dataAttr)(isDisabled),
        className: slots.selectedIcon({ class: classNames == null ? void 0 : classNames.selectedIcon }),
        ...props2
      };
    },
    [isDisabled, slots, classNames]
  );
  return {
    Component,
    domRef,
    slots,
    classNames,
    isSelectable,
    isSelected,
    isDisabled,
    rendered,
    shortcut,
    description,
    startContent,
    endContent,
    selectedIcon,
    disableAnimation,
    getItemProps,
    getLabelProps,
    hideSelectedIcon,
    getDescriptionProps,
    getKeyboardShortcutProps,
    getSelectedIconProps
  };
}

// src/menu-selected-icon.tsx
var import_jsx_runtime = require("react/jsx-runtime");
function MenuSelectedIcon(props) {
  const { isSelected, disableAnimation, ...otherProps } = props;
  return /* @__PURE__ */ (0, import_jsx_runtime.jsx)(
    "svg",
    {
      "aria-hidden": "true",
      "data-selected": isSelected,
      role: "presentation",
      viewBox: "0 0 17 18",
      ...otherProps,
      children: /* @__PURE__ */ (0, import_jsx_runtime.jsx)(
        "polyline",
        {
          fill: "none",
          points: "1 9 7 14 15 4",
          stroke: "currentColor",
          strokeDasharray: 22,
          strokeDashoffset: isSelected ? 44 : 66,
          strokeLinecap: "round",
          strokeLinejoin: "round",
          strokeWidth: 1.5,
          style: !disableAnimation ? {
            transition: "stroke-dashoffset 200ms ease"
          } : {}
        }
      )
    }
  );
}

// src/menu-item.tsx
var import_jsx_runtime2 = require("react/jsx-runtime");
var MenuItem = (props) => {
  const {
    Component,
    slots,
    classNames,
    rendered,
    shortcut,
    description,
    isSelectable,
    isSelected,
    isDisabled,
    selectedIcon,
    startContent,
    endContent,
    disableAnimation,
    hideSelectedIcon,
    getItemProps,
    getLabelProps,
    getDescriptionProps,
    getKeyboardShortcutProps,
    getSelectedIconProps
  } = useMenuItem(props);
  const selectedContent = (0, import_react3.useMemo)(() => {
    const defaultIcon = /* @__PURE__ */ (0, import_jsx_runtime2.jsx)(MenuSelectedIcon, { disableAnimation, isSelected });
    if (typeof selectedIcon === "function") {
      return selectedIcon({ icon: defaultIcon, isSelected, isDisabled });
    }
    if (selectedIcon) return selectedIcon;
    return defaultIcon;
  }, [selectedIcon, isSelected, isDisabled, disableAnimation]);
  return /* @__PURE__ */ (0, import_jsx_runtime2.jsxs)(Component, { ...getItemProps(), children: [
    startContent,
    description ? /* @__PURE__ */ (0, import_jsx_runtime2.jsxs)("div", { className: slots.wrapper({ class: classNames == null ? void 0 : classNames.wrapper }), children: [
      /* @__PURE__ */ (0, import_jsx_runtime2.jsx)("span", { ...getLabelProps(), children: rendered }),
      /* @__PURE__ */ (0, import_jsx_runtime2.jsx)("span", { ...getDescriptionProps(), children: description })
    ] }) : /* @__PURE__ */ (0, import_jsx_runtime2.jsx)("span", { ...getLabelProps(), children: rendered }),
    shortcut && /* @__PURE__ */ (0, import_jsx_runtime2.jsx)("kbd", { ...getKeyboardShortcutProps(), children: shortcut }),
    isSelectable && !hideSelectedIcon && /* @__PURE__ */ (0, import_jsx_runtime2.jsx)("span", { ...getSelectedIconProps(), children: selectedContent }),
    endContent
  ] });
};
MenuItem.displayName = "HeroUI.MenuItem";
var menu_item_default = MenuItem;

// src/menu-section.tsx
var import_jsx_runtime3 = require("react/jsx-runtime");
var MenuSection = (0, import_system3.forwardRef)(
  ({
    item,
    state,
    as,
    variant,
    color,
    disableAnimation,
    onAction,
    closeOnSelect,
    className,
    classNames,
    showDivider = false,
    hideSelectedIcon,
    dividerProps = {},
    itemClasses,
    // removed title from props to avoid browsers showing a tooltip on hover
    // the title props is already inside the rendered prop
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    title,
    ...otherProps
  }, _) => {
    const Component = as || "li";
    const slots = (0, import_react4.useMemo)(() => (0, import_theme3.menuSection)(), []);
    const baseStyles = (0, import_shared_utils3.clsx)(classNames == null ? void 0 : classNames.base, className);
    const dividerStyles = (0, import_shared_utils3.clsx)(classNames == null ? void 0 : classNames.divider, dividerProps == null ? void 0 : dividerProps.className);
    const { itemProps, headingProps, groupProps } = (0, import_menu3.useMenuSection)({
      heading: item.rendered,
      "aria-label": item["aria-label"]
    });
    return /* @__PURE__ */ (0, import_jsx_runtime3.jsxs)(
      Component,
      {
        "data-slot": "base",
        ...(0, import_utils2.mergeProps)(itemProps, otherProps),
        className: slots.base({ class: baseStyles }),
        children: [
          item.rendered && /* @__PURE__ */ (0, import_jsx_runtime3.jsx)(
            "span",
            {
              ...headingProps,
              className: slots.heading({ class: classNames == null ? void 0 : classNames.heading }),
              "data-slot": "heading",
              children: item.rendered
            }
          ),
          /* @__PURE__ */ (0, import_jsx_runtime3.jsxs)(
            "ul",
            {
              ...groupProps,
              className: slots.group({ class: classNames == null ? void 0 : classNames.group }),
              "data-has-title": !!item.rendered,
              "data-slot": "group",
              children: [
                [...item.childNodes].map((node) => {
                  const { key: nodeKey, props: nodeProps } = node;
                  let menuItem2 = /* @__PURE__ */ (0, import_jsx_runtime3.jsx)(
                    menu_item_default,
                    {
                      classNames: itemClasses,
                      closeOnSelect,
                      color,
                      disableAnimation,
                      hideSelectedIcon,
                      item: node,
                      state,
                      variant,
                      onAction,
                      ...nodeProps
                    },
                    nodeKey
                  );
                  if (node.wrapper) {
                    menuItem2 = node.wrapper(menuItem2);
                  }
                  return menuItem2;
                }),
                showDivider && /* @__PURE__ */ (0, import_jsx_runtime3.jsx)(
                  import_divider.Divider,
                  {
                    as: "li",
                    className: slots.divider({
                      class: dividerStyles
                    }),
                    ...dividerProps
                  }
                )
              ]
            }
          )
        ]
      }
    );
  }
);
MenuSection.displayName = "HeroUI.MenuSection";
var menu_section_default = MenuSection;

// src/menu.tsx
var import_jsx_runtime4 = require("react/jsx-runtime");
var Menu = (0, import_system4.forwardRef)(function Menu2(props, ref) {
  const {
    Component,
    state,
    closeOnSelect,
    color,
    disableAnimation,
    hideSelectedIcon,
    hideEmptyContent,
    variant,
    onClose,
    topContent,
    bottomContent,
    itemClasses,
    getBaseProps,
    getListProps,
    getEmptyContentProps
  } = useMenu({ ...props, ref });
  const content = /* @__PURE__ */ (0, import_jsx_runtime4.jsxs)(Component, { ...getListProps(), children: [
    !state.collection.size && !hideEmptyContent && /* @__PURE__ */ (0, import_jsx_runtime4.jsx)("li", { children: /* @__PURE__ */ (0, import_jsx_runtime4.jsx)("div", { ...getEmptyContentProps() }) }),
    [...state.collection].map((item) => {
      const itemProps = {
        closeOnSelect,
        color,
        disableAnimation,
        item,
        state,
        variant,
        onClose,
        hideSelectedIcon,
        ...item.props
      };
      const mergedItemClasses = (0, import_theme4.mergeClasses)(itemClasses, itemProps == null ? void 0 : itemProps.classNames);
      if (item.type === "section") {
        return /* @__PURE__ */ (0, import_jsx_runtime4.jsx)(menu_section_default, { ...itemProps, itemClasses: mergedItemClasses }, item.key);
      }
      let menuItem2 = /* @__PURE__ */ (0, import_jsx_runtime4.jsx)(menu_item_default, { ...itemProps, classNames: mergedItemClasses }, item.key);
      if (item.wrapper) {
        menuItem2 = item.wrapper(menuItem2);
      }
      return menuItem2;
    })
  ] });
  return /* @__PURE__ */ (0, import_jsx_runtime4.jsxs)("div", { ...getBaseProps(), children: [
    topContent,
    content,
    bottomContent
  ] });
});
var menu_default = Menu;
