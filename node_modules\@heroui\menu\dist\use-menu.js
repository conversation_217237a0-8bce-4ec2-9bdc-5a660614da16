"use client";
"use strict";
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);

// src/use-menu.ts
var use_menu_exports = {};
__export(use_menu_exports, {
  useMenu: () => useMenu
});
module.exports = __toCommonJS(use_menu_exports);
var import_system = require("@heroui/system");
var import_menu = require("@react-aria/menu");
var import_theme = require("@heroui/theme");
var import_tree = require("@react-stately/tree");
var import_react_utils = require("@heroui/react-utils");
var import_react = require("react");
var import_shared_utils = require("@heroui/shared-utils");
function useMenu(props) {
  var _a;
  const globalContext = (0, import_system.useProviderContext)();
  const {
    as,
    ref,
    variant,
    color,
    children,
    disableAnimation = (_a = globalContext == null ? void 0 : globalContext.disableAnimation) != null ? _a : false,
    onAction,
    closeOnSelect,
    itemClasses,
    className,
    state: propState,
    topContent,
    bottomContent,
    hideEmptyContent = false,
    hideSelectedIcon = false,
    emptyContent = "No items.",
    menuProps: userMenuProps,
    onClose,
    classNames,
    ...otherProps
  } = props;
  const Component = as || "ul";
  const domRef = (0, import_react_utils.useDOMRef)(ref);
  const shouldFilterDOMProps = typeof Component === "string";
  const innerState = (0, import_tree.useTreeState)({ ...otherProps, ...userMenuProps, children });
  const state = propState || innerState;
  const { menuProps } = (0, import_menu.useMenu)({ ...otherProps, ...userMenuProps, onAction }, state, domRef);
  const slots = (0, import_react.useMemo)(() => (0, import_theme.menu)({ className }), [className]);
  const baseStyles = (0, import_shared_utils.clsx)(classNames == null ? void 0 : classNames.base, className);
  const getBaseProps = (props2 = {}) => {
    return {
      ref: domRef,
      "data-slot": "base",
      className: slots.base({ class: baseStyles }),
      ...(0, import_react_utils.filterDOMProps)(otherProps, {
        enabled: shouldFilterDOMProps
      }),
      ...props2
    };
  };
  const getListProps = (props2 = {}) => {
    return {
      "data-slot": "list",
      className: slots.list({ class: classNames == null ? void 0 : classNames.list }),
      ...menuProps,
      ...props2
    };
  };
  const getEmptyContentProps = (props2 = {}) => {
    return {
      children: emptyContent,
      className: slots.emptyContent({ class: classNames == null ? void 0 : classNames.emptyContent }),
      ...props2
    };
  };
  return {
    Component,
    state,
    variant,
    color,
    disableAnimation,
    onClose,
    topContent,
    bottomContent,
    closeOnSelect,
    className,
    itemClasses,
    getBaseProps,
    getListProps,
    hideEmptyContent,
    hideSelectedIcon,
    getEmptyContentProps
  };
}
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
  useMenu
});
