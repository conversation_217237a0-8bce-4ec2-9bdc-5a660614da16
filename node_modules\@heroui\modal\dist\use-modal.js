"use client";
"use strict";
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);

// src/use-modal.ts
var use_modal_exports = {};
__export(use_modal_exports, {
  useModal: () => useModal
});
module.exports = __toCommonJS(use_modal_exports);
var import_use_aria_modal_overlay = require("@heroui/use-aria-modal-overlay");
var import_react = require("react");
var import_theme = require("@heroui/theme");
var import_system = require("@heroui/system");
var import_use_aria_button = require("@heroui/use-aria-button");
var import_focus = require("@react-aria/focus");
var import_shared_utils = require("@heroui/shared-utils");
var import_react_utils = require("@heroui/react-utils");
var import_overlays = require("@react-stately/overlays");
var import_utils = require("@react-aria/utils");
function useModal(originalProps) {
  var _a, _b, _c;
  const globalContext = (0, import_system.useProviderContext)();
  const [props, variantProps] = (0, import_system.mapPropsVariants)(originalProps, import_theme.modal.variantKeys);
  const {
    ref,
    as,
    className,
    classNames,
    isOpen,
    defaultOpen,
    onOpenChange,
    motionProps,
    closeButton,
    isDismissable = true,
    hideCloseButton = false,
    shouldBlockScroll = true,
    portalContainer,
    isKeyboardDismissDisabled = false,
    onClose,
    ...otherProps
  } = props;
  const Component = as || "section";
  const domRef = (0, import_react_utils.useDOMRef)(ref);
  const closeButtonRef = (0, import_react.useRef)(null);
  const [headerMounted, setHeaderMounted] = (0, import_react.useState)(false);
  const [bodyMounted, setBodyMounted] = (0, import_react.useState)(false);
  const disableAnimation = (_b = (_a = originalProps.disableAnimation) != null ? _a : globalContext == null ? void 0 : globalContext.disableAnimation) != null ? _b : false;
  const dialogId = (0, import_react.useId)();
  const headerId = (0, import_react.useId)();
  const bodyId = (0, import_react.useId)();
  const state = (0, import_overlays.useOverlayTriggerState)({
    isOpen,
    defaultOpen,
    onOpenChange: (isOpen2) => {
      onOpenChange == null ? void 0 : onOpenChange(isOpen2);
      if (!isOpen2) {
        onClose == null ? void 0 : onClose();
      }
    }
  });
  const { modalProps, underlayProps } = (0, import_use_aria_modal_overlay.useAriaModalOverlay)(
    {
      isDismissable,
      shouldBlockScroll,
      isKeyboardDismissDisabled
    },
    state,
    domRef
  );
  const { buttonProps: closeButtonProps } = (0, import_use_aria_button.useAriaButton)({ onPress: state.close }, closeButtonRef);
  const { isFocusVisible: isCloseButtonFocusVisible, focusProps: closeButtonFocusProps } = (0, import_focus.useFocusRing)();
  const baseStyles = (0, import_shared_utils.clsx)(classNames == null ? void 0 : classNames.base, className);
  const slots = (0, import_react.useMemo)(
    () => (0, import_theme.modal)({
      ...variantProps,
      disableAnimation
    }),
    [(0, import_shared_utils.objectToDeps)(variantProps), disableAnimation]
  );
  const getDialogProps = (props2 = {}, ref2 = null) => {
    var _a2;
    return {
      ref: (0, import_utils.mergeRefs)(ref2, domRef),
      ...(0, import_utils.mergeProps)(modalProps, otherProps, props2),
      className: slots.base({ class: (0, import_shared_utils.clsx)(baseStyles, props2.className) }),
      id: dialogId,
      "data-open": (0, import_shared_utils.dataAttr)(state.isOpen),
      "data-dismissable": (0, import_shared_utils.dataAttr)(isDismissable),
      "aria-modal": (0, import_shared_utils.dataAttr)(true),
      "data-placement": (_a2 = originalProps == null ? void 0 : originalProps.placement) != null ? _a2 : "right",
      "aria-labelledby": headerMounted ? headerId : void 0,
      "aria-describedby": bodyMounted ? bodyId : void 0
    };
  };
  const getBackdropProps = (0, import_react.useCallback)(
    (props2 = {}) => ({
      className: slots.backdrop({ class: classNames == null ? void 0 : classNames.backdrop }),
      onClick: () => state.close(),
      ...underlayProps,
      ...props2
    }),
    [slots, classNames, underlayProps]
  );
  const getCloseButtonProps = () => {
    return {
      role: "button",
      tabIndex: 0,
      "aria-label": "Close",
      "data-focus-visible": (0, import_shared_utils.dataAttr)(isCloseButtonFocusVisible),
      className: slots.closeButton({ class: classNames == null ? void 0 : classNames.closeButton }),
      ...(0, import_utils.mergeProps)(closeButtonProps, closeButtonFocusProps)
    };
  };
  return {
    Component,
    slots,
    domRef,
    headerId,
    bodyId,
    motionProps,
    classNames,
    isDismissable,
    closeButton,
    hideCloseButton,
    portalContainer,
    shouldBlockScroll,
    backdrop: (_c = originalProps.backdrop) != null ? _c : "opaque",
    isOpen: state.isOpen,
    onClose: state.close,
    disableAnimation,
    setBodyMounted,
    setHeaderMounted,
    getDialogProps,
    getBackdropProps,
    getCloseButtonProps
  };
}
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
  useModal
});
