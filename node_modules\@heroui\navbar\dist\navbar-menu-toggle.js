"use client";
"use strict";
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);

// src/navbar-menu-toggle.tsx
var navbar_menu_toggle_exports = {};
__export(navbar_menu_toggle_exports, {
  default: () => navbar_menu_toggle_default
});
module.exports = __toCommonJS(navbar_menu_toggle_exports);
var import_button = require("@react-aria/button");
var import_system = require("@heroui/system");
var import_react_utils2 = require("@heroui/react-utils");
var import_shared_utils = require("@heroui/shared-utils");
var import_toggle = require("@react-stately/toggle");
var import_focus = require("@react-aria/focus");
var import_utils = require("@react-aria/utils");
var import_interactions = require("@react-aria/interactions");
var import_react = require("react");

// src/navbar-context.ts
var import_react_utils = require("@heroui/react-utils");
var [NavbarProvider, useNavbarContext] = (0, import_react_utils.createContext)({
  name: "NavbarContext",
  strict: true,
  errorMessage: "useNavbarContext: `context` is undefined. Seems you forgot to wrap component within <Navbar />"
});

// src/navbar-menu-toggle.tsx
var import_jsx_runtime = require("react/jsx-runtime");
var NavbarMenuToggle = (0, import_system.forwardRef)((props, ref) => {
  var _a;
  const {
    as,
    icon,
    className,
    onChange,
    autoFocus,
    srOnlyText: srOnlyTextProp,
    ...otherProps
  } = props;
  const Component = as || "button";
  const domRef = (0, import_react_utils2.useDOMRef)(ref);
  const { slots, classNames, isMenuOpen, setIsMenuOpen } = useNavbarContext();
  const handleChange = (isOpen) => {
    onChange == null ? void 0 : onChange(isOpen);
    setIsMenuOpen(isOpen);
  };
  const state = (0, import_toggle.useToggleState)({ ...otherProps, isSelected: isMenuOpen, onChange: handleChange });
  const { buttonProps, isPressed } = (0, import_button.useToggleButton)(props, state, domRef);
  const { isFocusVisible, focusProps } = (0, import_focus.useFocusRing)({ autoFocus });
  const { isHovered, hoverProps } = (0, import_interactions.useHover)({});
  const toggleStyles = (0, import_shared_utils.clsx)(classNames == null ? void 0 : classNames.toggle, className);
  const child = (0, import_react.useMemo)(() => {
    if (typeof icon === "function") {
      return icon(isMenuOpen != null ? isMenuOpen : false);
    }
    return icon || /* @__PURE__ */ (0, import_jsx_runtime.jsx)("span", { className: slots.toggleIcon({ class: classNames == null ? void 0 : classNames.toggleIcon }) });
  }, [icon, isMenuOpen, slots.toggleIcon, classNames == null ? void 0 : classNames.toggleIcon]);
  const srOnlyText = (0, import_react.useMemo)(() => {
    if (srOnlyTextProp) {
      return srOnlyTextProp;
    }
    return state.isSelected ? "close navigation menu" : "open navigation menu";
  }, [srOnlyTextProp, isMenuOpen]);
  return /* @__PURE__ */ (0, import_jsx_runtime.jsxs)(
    Component,
    {
      ref: domRef,
      className: (_a = slots.toggle) == null ? void 0 : _a.call(slots, { class: toggleStyles }),
      "data-focus-visible": (0, import_shared_utils.dataAttr)(isFocusVisible),
      "data-hover": (0, import_shared_utils.dataAttr)(isHovered),
      "data-open": (0, import_shared_utils.dataAttr)(isMenuOpen),
      "data-pressed": (0, import_shared_utils.dataAttr)(isPressed),
      ...(0, import_utils.mergeProps)(buttonProps, focusProps, hoverProps, otherProps),
      children: [
        /* @__PURE__ */ (0, import_jsx_runtime.jsx)("span", { className: slots.srOnly(), children: srOnlyText }),
        child
      ]
    }
  );
});
NavbarMenuToggle.displayName = "HeroUI.NavbarMenuToggle";
var navbar_menu_toggle_default = NavbarMenuToggle;
