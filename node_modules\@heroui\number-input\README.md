# @heroui/number-input

NumberInput is a component that allows users to enter number. It can be used to get user inputs in forms, search fields, and more.

Please refer to the [documentation](https://heroui.com/docs/components/number-input) for more information.

## Installation

```sh
yarn add @heroui/number-input
# or
npm i @heroui/number-input
```

## Contribution

Yes please! See the
[contributing guidelines](https://github.com/heroui-inc/heroui/blob/master/CONTRIBUTING.md)
for details.

## License

This project is licensed under the terms of the
[MIT license](https://github.com/heroui-inc/heroui/blob/master/LICENSE).
