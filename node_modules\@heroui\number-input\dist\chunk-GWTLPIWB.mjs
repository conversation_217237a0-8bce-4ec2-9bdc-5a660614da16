"use client";
import {
  number_input_stepper_default
} from "./chunk-HVA7EZPG.mjs";
import {
  useNumberInput
} from "./chunk-7XFMF2ZO.mjs";

// src/number-input.tsx
import { CloseFilledIcon } from "@heroui/shared-icons";
import { useMemo } from "react";
import { forwardRef } from "@heroui/system";
import { Fragment, jsx, jsxs } from "react/jsx-runtime";
var NumberInput = forwardRef((props, ref) => {
  const {
    Component,
    label,
    description,
    isClearable,
    startContent,
    endContent,
    labelPlacement,
    hasHelper,
    isOutsideLeft,
    shouldLabelBeOutside,
    errorMessage,
    isInvalid,
    hideStepper,
    getBaseProps,
    getLabelProps,
    getNumberInputProps,
    getHiddenNumberInputProps,
    getInnerWrapperProps,
    getInputWrapperProps,
    getMainWrapperProps,
    getHelperWrapperProps,
    getDescriptionProps,
    getErrorMessageProps,
    getClearButtonProps,
    getStepperIncreaseButtonProps,
    getStepperDecreaseButtonProps,
    getStepperWrapperProps
  } = useNumberInput({ ...props, ref });
  const labelContent = label ? /* @__PURE__ */ jsx("label", { ...getLabelProps(), children: label }) : null;
  const end = useMemo(() => {
    if (isClearable) {
      return /* @__PURE__ */ jsxs(Fragment, { children: [
        /* @__PURE__ */ jsx("button", { ...getClearButtonProps(), children: /* @__PURE__ */ jsx(CloseFilledIcon, {}) }),
        endContent
      ] });
    }
    return endContent;
  }, [isClearable, getClearButtonProps]);
  const helperWrapper = useMemo(() => {
    const shouldShowError = isInvalid && errorMessage;
    const hasContent = shouldShowError || description;
    if (!hasHelper || !hasContent) return null;
    return /* @__PURE__ */ jsx("div", { ...getHelperWrapperProps(), children: shouldShowError ? /* @__PURE__ */ jsx("div", { ...getErrorMessageProps(), children: errorMessage }) : /* @__PURE__ */ jsx("div", { ...getDescriptionProps(), children: description }) });
  }, [
    hasHelper,
    isInvalid,
    errorMessage,
    description,
    getHelperWrapperProps,
    getErrorMessageProps,
    getDescriptionProps
  ]);
  const innerWrapper = useMemo(() => {
    return /* @__PURE__ */ jsxs("div", { ...getInnerWrapperProps(), children: [
      startContent,
      /* @__PURE__ */ jsx("input", { ...getNumberInputProps() }),
      /* @__PURE__ */ jsx("input", { ...getHiddenNumberInputProps() }),
      end,
      !hideStepper && /* @__PURE__ */ jsxs("div", { ...getStepperWrapperProps(), children: [
        /* @__PURE__ */ jsx(number_input_stepper_default, { ...getStepperIncreaseButtonProps(), direction: "up" }),
        /* @__PURE__ */ jsx(number_input_stepper_default, { ...getStepperDecreaseButtonProps(), direction: "down" })
      ] })
    ] });
  }, [startContent, end, getNumberInputProps, getInnerWrapperProps]);
  const mainWrapper = useMemo(() => {
    if (shouldLabelBeOutside) {
      return /* @__PURE__ */ jsxs("div", { ...getMainWrapperProps(), children: [
        /* @__PURE__ */ jsxs("div", { ...getInputWrapperProps(), children: [
          !isOutsideLeft ? labelContent : null,
          innerWrapper
        ] }),
        helperWrapper
      ] });
    }
    return /* @__PURE__ */ jsxs(Fragment, { children: [
      /* @__PURE__ */ jsxs("div", { ...getInputWrapperProps(), children: [
        labelContent,
        innerWrapper
      ] }),
      helperWrapper
    ] });
  }, [
    labelPlacement,
    helperWrapper,
    shouldLabelBeOutside,
    labelContent,
    innerWrapper,
    errorMessage,
    description,
    getMainWrapperProps,
    getInputWrapperProps,
    getErrorMessageProps,
    getDescriptionProps
  ]);
  return /* @__PURE__ */ jsxs(Component, { ...getBaseProps(), children: [
    isOutsideLeft ? labelContent : null,
    mainWrapper
  ] });
});
NumberInput.displayName = "HeroUI.NumberInput";
var number_input_default = NumberInput;

export {
  number_input_default
};
