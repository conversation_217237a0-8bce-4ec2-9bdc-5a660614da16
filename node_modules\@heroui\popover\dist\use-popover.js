"use client";
"use strict";
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);

// src/use-popover.ts
var use_popover_exports = {};
__export(use_popover_exports, {
  usePopover: () => usePopover
});
module.exports = __toCommonJS(use_popover_exports);
var import_react2 = require("react");
var import_react_utils = require("@heroui/react-utils");
var import_overlays2 = require("@react-stately/overlays");
var import_focus = require("@react-aria/focus");
var import_overlays3 = require("@react-aria/overlays");
var import_aria_utils2 = require("@heroui/aria-utils");
var import_system = require("@heroui/system");
var import_aria_utils3 = require("@heroui/aria-utils");
var import_theme = require("@heroui/theme");
var import_utils2 = require("@react-aria/utils");
var import_shared_utils = require("@heroui/shared-utils");
var import_react3 = require("react");

// src/use-aria-popover.ts
var import_aria_utils = require("@heroui/aria-utils");
var import_overlays = require("@react-aria/overlays");
var import_react = require("react");
var import_utils = require("@react-aria/utils");
var import_use_safe_layout_effect = require("@heroui/use-safe-layout-effect");
function useReactAriaPopover(props, state) {
  const {
    groupRef,
    triggerRef,
    popoverRef,
    showArrow,
    offset = 7,
    crossOffset = 0,
    scrollRef,
    shouldFlip,
    boundaryElement,
    isDismissable = true,
    shouldCloseOnBlur = true,
    shouldCloseOnScroll = true,
    placement: placementProp = "top",
    containerPadding,
    shouldCloseOnInteractOutside,
    isNonModal: isNonModalProp,
    isKeyboardDismissDisabled,
    updatePositionDeps = [],
    ...otherProps
  } = props;
  const isNonModal = isNonModalProp != null ? isNonModalProp : true;
  const isSubmenu = otherProps["trigger"] === "SubmenuTrigger";
  const { overlayProps, underlayProps } = (0, import_overlays.useOverlay)(
    {
      isOpen: state.isOpen,
      onClose: state.close,
      shouldCloseOnBlur,
      isDismissable: isDismissable || isSubmenu,
      isKeyboardDismissDisabled,
      shouldCloseOnInteractOutside: shouldCloseOnInteractOutside ? shouldCloseOnInteractOutside : (element) => (0, import_aria_utils.ariaShouldCloseOnInteractOutside)(element, triggerRef, state)
    },
    popoverRef
  );
  const {
    overlayProps: positionProps,
    arrowProps,
    placement,
    updatePosition
  } = (0, import_overlays.useOverlayPosition)({
    ...otherProps,
    shouldFlip,
    crossOffset,
    targetRef: triggerRef,
    overlayRef: popoverRef,
    isOpen: state.isOpen,
    scrollRef,
    boundaryElement,
    containerPadding,
    placement: (0, import_aria_utils.toReactAriaPlacement)(placementProp),
    offset: showArrow ? offset + 3 : offset,
    onClose: isNonModal && !isSubmenu && shouldCloseOnScroll ? state.close : () => {
    }
  });
  (0, import_use_safe_layout_effect.useSafeLayoutEffect)(() => {
    if (!updatePositionDeps.length) return;
    updatePosition();
  }, updatePositionDeps);
  (0, import_react.useEffect)(() => {
    var _a, _b;
    if (state.isOpen && popoverRef.current) {
      if (isNonModal) {
        return (0, import_aria_utils.keepVisible)((_a = groupRef == null ? void 0 : groupRef.current) != null ? _a : popoverRef.current);
      } else {
        return (0, import_aria_utils.ariaHideOutside)([(_b = groupRef == null ? void 0 : groupRef.current) != null ? _b : popoverRef.current]);
      }
    }
  }, [isNonModal, state.isOpen, popoverRef, groupRef]);
  return {
    popoverProps: (0, import_utils.mergeProps)(overlayProps, positionProps),
    arrowProps,
    underlayProps,
    placement
  };
}

// src/use-popover.ts
var DEFAULT_PLACEMENT = "top";
function usePopover(originalProps) {
  var _a, _b, _c;
  const globalContext = (0, import_system.useProviderContext)();
  const [props, variantProps] = (0, import_system.mapPropsVariants)(originalProps, import_theme.popover.variantKeys);
  const {
    as,
    ref,
    children,
    state: stateProp,
    triggerRef: triggerRefProp,
    scrollRef,
    defaultOpen,
    onOpenChange,
    isOpen: isOpenProp,
    isNonModal = true,
    shouldFlip = true,
    containerPadding = 12,
    shouldBlockScroll = false,
    isDismissable = true,
    shouldCloseOnBlur,
    portalContainer,
    updatePositionDeps,
    dialogProps: dialogPropsProp,
    placement: placementProp = DEFAULT_PLACEMENT,
    triggerType = "dialog",
    showArrow = false,
    offset = 7,
    crossOffset = 0,
    boundaryElement,
    isKeyboardDismissDisabled,
    shouldCloseOnInteractOutside,
    shouldCloseOnScroll,
    motionProps,
    className,
    classNames,
    onClose,
    ...otherProps
  } = props;
  const Component = as || "div";
  const domRef = (0, import_react_utils.useDOMRef)(ref);
  const domTriggerRef = (0, import_react3.useRef)(null);
  const wasTriggerPressedRef = (0, import_react3.useRef)(false);
  const triggerRef = triggerRefProp || domTriggerRef;
  const disableAnimation = (_b = (_a = originalProps.disableAnimation) != null ? _a : globalContext == null ? void 0 : globalContext.disableAnimation) != null ? _b : false;
  const innerState = (0, import_overlays2.useOverlayTriggerState)({
    isOpen: isOpenProp,
    defaultOpen,
    onOpenChange: (isOpen) => {
      onOpenChange == null ? void 0 : onOpenChange(isOpen);
      if (!isOpen) {
        onClose == null ? void 0 : onClose();
      }
    }
  });
  const state = stateProp || innerState;
  const {
    popoverProps,
    underlayProps,
    placement: ariaPlacement
  } = useReactAriaPopover(
    {
      triggerRef,
      isNonModal,
      popoverRef: domRef,
      placement: placementProp,
      offset,
      scrollRef,
      isDismissable,
      shouldCloseOnBlur,
      boundaryElement,
      crossOffset,
      shouldFlip,
      containerPadding,
      updatePositionDeps,
      isKeyboardDismissDisabled,
      shouldCloseOnScroll,
      shouldCloseOnInteractOutside
    },
    state
  );
  const placement = (0, import_react3.useMemo)(() => {
    if (!ariaPlacement) {
      return null;
    }
    return (0, import_aria_utils2.getShouldUseAxisPlacement)(ariaPlacement, placementProp) ? ariaPlacement : placementProp;
  }, [ariaPlacement, placementProp]);
  const { triggerProps } = (0, import_overlays3.useOverlayTrigger)({ type: triggerType }, state, triggerRef);
  const { isFocusVisible, isFocused, focusProps } = (0, import_focus.useFocusRing)();
  const slots = (0, import_react3.useMemo)(
    () => (0, import_theme.popover)({
      ...variantProps
    }),
    [(0, import_shared_utils.objectToDeps)(variantProps)]
  );
  const baseStyles = (0, import_shared_utils.clsx)(classNames == null ? void 0 : classNames.base, className);
  (0, import_overlays3.usePreventScroll)({
    isDisabled: !(shouldBlockScroll && state.isOpen)
  });
  const getPopoverProps = (props2 = {}) => ({
    ref: domRef,
    ...(0, import_utils2.mergeProps)(popoverProps, otherProps, props2),
    style: (0, import_utils2.mergeProps)(popoverProps.style, otherProps.style, props2.style)
  });
  const getDialogProps = (props2 = {}) => ({
    // `ref` and `dialogProps` from `useDialog` are passed from props
    // if we use `useDialog` here, dialogRef won't be focused on mount
    "data-slot": "base",
    "data-open": (0, import_shared_utils.dataAttr)(state.isOpen),
    "data-focus": (0, import_shared_utils.dataAttr)(isFocused),
    "data-arrow": (0, import_shared_utils.dataAttr)(showArrow),
    "data-focus-visible": (0, import_shared_utils.dataAttr)(isFocusVisible),
    "data-placement": ariaPlacement ? (0, import_aria_utils3.getArrowPlacement)(ariaPlacement, placementProp) : void 0,
    ...(0, import_utils2.mergeProps)(focusProps, dialogPropsProp, props2),
    className: slots.base({ class: (0, import_shared_utils.clsx)(baseStyles) }),
    style: {
      // this prevent the dialog to have a default outline
      outline: "none"
    }
  });
  const getContentProps = (0, import_react3.useCallback)(
    (props2 = {}) => ({
      "data-slot": "content",
      "data-open": (0, import_shared_utils.dataAttr)(state.isOpen),
      "data-arrow": (0, import_shared_utils.dataAttr)(showArrow),
      "data-placement": ariaPlacement ? (0, import_aria_utils3.getArrowPlacement)(ariaPlacement, placementProp) : void 0,
      className: slots.content({ class: (0, import_shared_utils.clsx)(classNames == null ? void 0 : classNames.content, props2.className) })
    }),
    [slots, state.isOpen, showArrow, placement, placementProp, classNames, ariaPlacement]
  );
  const onPress = (0, import_react3.useCallback)(
    (e) => {
      var _a2;
      let pressTimer;
      if (e.pointerType === "touch" && ((originalProps == null ? void 0 : originalProps.backdrop) === "blur" || (originalProps == null ? void 0 : originalProps.backdrop) === "opaque")) {
        pressTimer = setTimeout(() => {
          wasTriggerPressedRef.current = true;
        }, 100);
      } else {
        wasTriggerPressedRef.current = true;
      }
      (_a2 = triggerProps.onPress) == null ? void 0 : _a2.call(triggerProps, e);
      return () => {
        clearTimeout(pressTimer);
      };
    },
    [triggerProps == null ? void 0 : triggerProps.onPress]
  );
  const getTriggerProps = (0, import_react3.useCallback)(
    (props2 = {}, _ref = null) => {
      const { isDisabled, ...otherProps2 } = props2;
      return {
        "data-slot": "trigger",
        ...(0, import_utils2.mergeProps)({ "aria-haspopup": "dialog" }, triggerProps, otherProps2),
        onPress,
        isDisabled,
        className: slots.trigger({
          class: (0, import_shared_utils.clsx)(classNames == null ? void 0 : classNames.trigger, props2.className),
          // apply isDisabled class names to make the trigger child disabled
          // e.g. for elements like div or HeroUI elements that don't have `isDisabled` prop
          isTriggerDisabled: isDisabled
        }),
        ref: (0, import_utils2.mergeRefs)(_ref, triggerRef)
      };
    },
    [state, triggerProps, onPress, triggerRef]
  );
  const getBackdropProps = (0, import_react3.useCallback)(
    (props2 = {}) => ({
      "data-slot": "backdrop",
      className: slots.backdrop({ class: classNames == null ? void 0 : classNames.backdrop }),
      onClick: (e) => {
        if (!wasTriggerPressedRef.current) {
          e.preventDefault();
          return;
        }
        state.close();
        wasTriggerPressedRef.current = false;
      },
      ...underlayProps,
      ...props2
    }),
    [slots, state.isOpen, classNames, underlayProps]
  );
  (0, import_react2.useEffect)(() => {
    if (state.isOpen && (domRef == null ? void 0 : domRef.current)) {
      return (0, import_overlays3.ariaHideOutside)([domRef == null ? void 0 : domRef.current]);
    }
  }, [state.isOpen, domRef]);
  return {
    state,
    Component,
    children,
    classNames,
    showArrow,
    triggerRef,
    placement,
    isNonModal,
    popoverRef: domRef,
    portalContainer,
    isOpen: state.isOpen,
    onClose: state.close,
    disableAnimation,
    shouldBlockScroll,
    backdrop: (_c = originalProps.backdrop) != null ? _c : "transparent",
    motionProps,
    getBackdropProps,
    getPopoverProps,
    getTriggerProps,
    getDialogProps,
    getContentProps
  };
}
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
  usePopover
});
