{"name": "@heroui/popover", "version": "2.3.22", "description": "A popover is an overlay element positioned relative to a trigger.", "keywords": ["popover"], "author": "HeroUI <<EMAIL>>", "homepage": "https://heroui.com", "license": "MIT", "main": "dist/index.js", "sideEffects": false, "files": ["dist"], "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "git+https://github.com/heroui-inc/heroui.git", "directory": "packages/components/popover"}, "bugs": {"url": "https://github.com/heroui-inc/heroui/issues"}, "peerDependencies": {"@heroui/system": ">=2.4.17", "@heroui/theme": ">=2.4.6", "framer-motion": ">=11.5.6 || >=12.0.0-alpha.1", "react": ">=18 || >=19.0.0-rc.0", "react-dom": ">=18 || >=19.0.0-rc.0"}, "dependencies": {"@react-aria/dialog": "3.5.27", "@react-aria/focus": "3.20.5", "@react-aria/overlays": "3.27.3", "@react-aria/utils": "3.29.1", "@react-stately/overlays": "3.6.17", "@react-types/overlays": "3.8.16", "@heroui/button": "2.2.22", "@heroui/framer-utils": "2.1.18", "@heroui/aria-utils": "2.2.19", "@heroui/react-utils": "2.1.11", "@heroui/use-aria-button": "2.2.16", "@heroui/shared-utils": "2.1.9", "@heroui/use-safe-layout-effect": "2.1.7", "@heroui/dom-animation": "2.1.9"}, "clean-package": "../../../clean-package.config.json", "module": "dist/index.mjs", "types": "dist/index.d.ts", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.js"}, "./package.json": "./package.json"}, "scripts": {"build": "tsup src --dts", "build:fast": "tsup src", "dev": "pnpm build:fast --watch", "clean": "rimraf dist .turbo", "typecheck": "tsc --noEmit"}}