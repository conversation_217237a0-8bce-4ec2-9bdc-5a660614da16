"use client";
import {
  useRadio
} from "./chunk-LXQE2FYM.mjs";

// src/radio.tsx
import { forwardRef } from "@heroui/system";
import { jsx, jsxs } from "react/jsx-runtime";
var Radio = forwardRef((props, ref) => {
  const {
    Component,
    children,
    description,
    getBaseProps,
    getWrapperProps,
    getInputProps,
    getLabelProps,
    getLabelWrapperProps,
    getControlProps,
    getDescriptionProps
  } = useRadio({ ...props, ref });
  return /* @__PURE__ */ jsxs(Component, { ...getBaseProps(), children: [
    /* @__PURE__ */ jsx("input", { ...getInputProps() }),
    /* @__PURE__ */ jsx("span", { ...getWrapperProps(), children: /* @__PURE__ */ jsx("span", { ...getControlProps() }) }),
    /* @__PURE__ */ jsxs("div", { ...getLabelWrapperProps(), children: [
      children && /* @__PURE__ */ jsx("span", { ...getLabelProps(), children }),
      description && /* @__PURE__ */ jsx("span", { ...getDescriptionProps(), children: description })
    ] })
  ] });
});
Radio.displayName = "HeroUI.Radio";
var radio_default = Radio;

export {
  radio_default
};
