# @heroui/select

A select displays a collapsible list of options and allows a user to select one of them.

Please refer to the [documentation](https://heroui.com/docs/components/select) for more information.

## Installation

```sh
yarn add @heroui/select
# or
npm i @heroui/select
```

## Contribution

Yes please! See the
[contributing guidelines](https://github.com/heroui-inc/heroui/blob/master/CONTRIBUTING.md)
for details.

## License

This project is licensed under the terms of the
[MIT license](https://github.com/heroui-inc/heroui/blob/master/LICENSE).
