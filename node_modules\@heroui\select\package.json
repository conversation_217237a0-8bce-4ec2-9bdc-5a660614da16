{"name": "@heroui/select", "version": "2.4.22", "description": "A select displays a collapsible list of options and allows a user to select one of them.", "keywords": ["select"], "author": "HeroUI <<EMAIL>>", "homepage": "https://heroui.com", "license": "MIT", "main": "dist/index.js", "sideEffects": false, "files": ["dist"], "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "git+https://github.com/heroui-inc/heroui.git", "directory": "packages/components/select"}, "bugs": {"url": "https://github.com/heroui-inc/heroui/issues"}, "peerDependencies": {"@heroui/system": ">=2.4.17", "@heroui/theme": ">=2.4.12", "framer-motion": ">=11.5.6 || >=12.0.0-alpha.1", "react": ">=18 || >=19.0.0-rc.0", "react-dom": ">=18 || >=19.0.0-rc.0"}, "dependencies": {"@react-aria/focus": "3.20.5", "@react-aria/form": "3.0.18", "@react-aria/overlays": "3.27.3", "@react-aria/interactions": "3.25.3", "@react-aria/utils": "3.29.1", "@react-aria/visually-hidden": "3.8.25", "@react-types/shared": "3.30.0", "@heroui/aria-utils": "2.2.19", "@heroui/react-utils": "2.1.11", "@heroui/listbox": "2.3.21", "@heroui/shared-icons": "2.1.9", "@heroui/popover": "2.3.22", "@heroui/scroll-shadow": "2.3.15", "@heroui/shared-utils": "2.1.9", "@heroui/spinner": "2.2.19", "@heroui/use-aria-button": "2.2.16", "@heroui/use-aria-multiselect": "2.4.15", "@heroui/use-safe-layout-effect": "2.1.7", "@heroui/form": "2.1.21"}, "clean-package": "../../../clean-package.config.json", "module": "dist/index.mjs", "types": "dist/index.d.ts", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.js"}, "./package.json": "./package.json"}, "scripts": {"build": "tsup src --dts", "build:fast": "tsup src", "dev": "pnpm build:fast --watch", "clean": "rimraf dist .turbo", "typecheck": "tsc --noEmit"}}