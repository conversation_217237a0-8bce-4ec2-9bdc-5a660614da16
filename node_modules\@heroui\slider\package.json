{"name": "@heroui/slider", "version": "2.4.19", "description": "A slider allows a user to select one or more values within a range.", "keywords": ["slider"], "author": "HeroUI <<EMAIL>>", "homepage": "https://heroui.com", "license": "MIT", "main": "dist/index.js", "sideEffects": false, "files": ["dist"], "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "git+https://github.com/heroui-inc/heroui.git", "directory": "packages/components/slider"}, "bugs": {"url": "https://github.com/heroui-inc/heroui/issues"}, "peerDependencies": {"react": ">=18 || >=19.0.0-rc.0", "react-dom": ">=18 || >=19.0.0-rc.0", "@heroui/theme": ">=2.4.6", "@heroui/system": ">=2.4.17"}, "dependencies": {"@react-aria/focus": "3.20.5", "@react-aria/i18n": "3.12.10", "@react-aria/interactions": "3.25.3", "@react-aria/slider": "3.7.21", "@react-aria/utils": "3.29.1", "@react-aria/visually-hidden": "3.8.25", "@react-stately/slider": "3.6.5", "@heroui/shared-utils": "2.1.9", "@heroui/react-utils": "2.1.11", "@heroui/tooltip": "2.2.19"}, "clean-package": "../../../clean-package.config.json", "module": "dist/index.mjs", "types": "dist/index.d.ts", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.js"}, "./package.json": "./package.json"}, "scripts": {"build": "tsup src --dts", "build:fast": "tsup src", "dev": "pnpm build:fast --watch", "clean": "rimraf dist .turbo", "typecheck": "tsc --noEmit"}}