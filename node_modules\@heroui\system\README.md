# @heroui/system

HeroUI system primitives, here you can find the `extendVariants` utility, `HeroUIProvider` and some other utilities.

Please refer to the [documentation](https://heroui.com) for more information.

## Installation

```sh
yarn add @heroui/system
# or
npm i @heroui/system
```

## Contribution

Yes please! See the
[contributing guidelines](https://github.com/heroui-inc/heroui/blob/master/CONTRIBUTING.md)
for details.

## License

This project is licensed under the terms of the
[MIT license](https://github.com/heroui-inc/heroui/blob/master/LICENSE).
