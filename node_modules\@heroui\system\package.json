{"name": "@heroui/system", "version": "2.4.18", "description": "HeroUI system primitives", "keywords": ["system"], "author": "HeroUI <<EMAIL>>", "homepage": "https://heroui.com", "license": "MIT", "main": "dist/index.js", "sideEffects": false, "files": ["dist"], "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "git+https://github.com/heroui-inc/heroui.git", "directory": "packages/core/system"}, "bugs": {"url": "https://github.com/heroui-inc/heroui/issues"}, "peerDependencies": {"framer-motion": ">=11.5.6 || >=12.0.0-alpha.1", "react": ">=18 || >=19.0.0-rc.0", "react-dom": ">=18 || >=19.0.0-rc.0"}, "clean-package": "../../../clean-package.config.json", "tsup": {"clean": true, "target": "es2019", "format": ["cjs", "esm"]}, "dependencies": {"@react-aria/i18n": "3.12.10", "@react-aria/overlays": "3.27.3", "@react-aria/utils": "3.29.1", "@heroui/react-utils": "2.1.11", "@heroui/system-rsc": "2.3.15"}, "module": "dist/index.mjs", "types": "dist/index.d.ts", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.js"}, "./package.json": "./package.json"}, "scripts": {"build": "tsup src !src/extend-variants.d.ts --dts", "dev": "pnpm build:fast --watch", "clean": "rimraf dist .turbo", "typecheck": "tsc --noEmit", "build:fast": "tsup src !src/extend-variants.d.ts"}}