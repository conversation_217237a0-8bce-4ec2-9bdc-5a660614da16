{"name": "@heroui/tabs", "version": "2.2.19", "description": "Tabs organize content into multiple sections and allow users to navigate between them.", "keywords": ["tabs"], "author": "HeroUI <<EMAIL>>", "homepage": "https://heroui.com", "license": "MIT", "main": "dist/index.js", "sideEffects": false, "files": ["dist"], "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "git+https://github.com/heroui-inc/heroui.git", "directory": "packages/components/tabs"}, "bugs": {"url": "https://github.com/heroui-inc/heroui/issues"}, "peerDependencies": {"react": ">=18 || >=19.0.0-rc.0", "react-dom": ">=18 || >=19.0.0-rc.0", "framer-motion": ">=11.5.6 || >=12.0.0-alpha.1", "@heroui/theme": ">=2.4.6", "@heroui/system": ">=2.4.17"}, "dependencies": {"@react-aria/focus": "3.20.5", "@react-aria/interactions": "3.25.3", "@react-aria/tabs": "3.10.5", "@react-aria/utils": "3.29.1", "@react-stately/tabs": "3.8.3", "@react-types/shared": "3.30.0", "scroll-into-view-if-needed": "3.0.10", "@heroui/shared-utils": "2.1.9", "@heroui/react-utils": "2.1.11", "@heroui/aria-utils": "2.2.19", "@heroui/use-is-mounted": "2.1.7"}, "clean-package": "../../../clean-package.config.json", "module": "dist/index.mjs", "types": "dist/index.d.ts", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.js"}, "./package.json": "./package.json"}, "scripts": {"build": "tsup src --dts", "build:fast": "tsup src", "dev": "pnpm build:fast --watch", "clean": "rimraf dist .turbo", "typecheck": "tsc --noEmit"}}