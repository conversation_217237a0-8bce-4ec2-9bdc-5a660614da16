{"name": "@heroui/toast", "version": "2.0.12", "description": "Toast are temporary notifications that provide concise feedback about an action or event", "keywords": ["toast"], "author": "HeroUI <<EMAIL>>", "homepage": "https://heroui.com", "license": "MIT", "main": "dist/index.js", "sideEffects": false, "files": ["dist"], "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "git+https://github.com/heroui-inc/heroui.git", "directory": "packages/components/toast"}, "bugs": {"url": "https://github.com/heroui-inc/heroui/issues"}, "peerDependencies": {"@heroui/system": ">=2.4.17", "@heroui/theme": ">=2.4.12", "react": ">=18 || >=19.0.0-rc.0", "react-dom": ">=18 || >=19.0.0-rc.0", "framer-motion": ">=11.5.6 || >=12.0.0-alpha.1"}, "dependencies": {"@react-aria/toast": "3.0.5", "@react-aria/utils": "3.29.1", "@react-aria/interactions": "3.25.3", "@react-stately/toast": "3.1.1", "@heroui/react-utils": "2.1.11", "@heroui/shared-utils": "2.1.9", "@heroui/shared-icons": "2.1.9", "@heroui/spinner": "2.2.19", "@heroui/use-is-mobile": "2.2.10"}, "clean-package": "../../../clean-package.config.json", "module": "dist/index.mjs", "types": "dist/index.d.ts", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.js"}, "./package.json": "./package.json"}, "scripts": {"build": "tsup src --dts", "build:fast": "tsup src", "dev": "pnpm build:fast --watch", "clean": "rimraf dist .turbo", "typecheck": "tsc --noEmit"}}