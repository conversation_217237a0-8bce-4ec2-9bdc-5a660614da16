{"mappings": ";;;;;;;;;;;;;;;AAAA;;;;;;;;;;CAUC;;;AAqBD,MAAM,+BAAwB;IAC5B,QAAQ;IACR,MAAM;IACN,UAAU;IACV,QAAQ;IACR,QAAQ;IACR,UAAU;IACV,SAAS;IACT,UAAU;IACV,OAAO;IACP,YAAY;AACd;AAUO,SAAS,0CAAkB,QAA6B,CAAC,CAAC;IAC/D,IAAI,SACF,KAAK,eACL,WAAW,EACZ,GAAG;IAEJ,IAAI,CAAC,WAAW,WAAW,GAAG,CAAA,GAAA,qBAAO,EAAE;IACvC,IAAI,oBAAC,gBAAgB,EAAC,GAAG,CAAA,GAAA,2CAAa,EAAE;QACtC,YAAY,CAAC;QACb,qBAAqB,CAAC,MAAQ,WAAW;IAC3C;IAEA,sCAAsC;IACtC,IAAI,iBAAiB,CAAA,GAAA,oBAAM,EAAE;QAC3B,IAAI,WACF,OAAO;aACF,IAAI,OACT,OAAO;YAAC,GAAG,4BAAM;YAAE,GAAG,KAAK;QAAA;aAE3B,OAAO;IAEX,uDAAuD;IACvD,GAAG;QAAC;KAAU;IAEd,OAAO;QACL,qBAAqB;YACnB,GAAG,gBAAgB;YACnB,OAAO;QACT;IACF;AACF;AAMO,SAAS,0CAAe,KAA0B;IACvD,6DAA6D;IAC7D,IAAI,YAAC,QAAQ,EAAE,aAAa,UAAU,KAAK,eAAE,WAAW,SAAE,KAAK,EAAE,GAAG,YAAW,GAAG;IAClF,IAAI,uBAAC,mBAAmB,EAAC,GAAG,0CAAkB;IAE9C,qBACE,0DAAC,SAAY,CAAA,GAAA,gCAAS,EAAE,YAAY,sBACjC;AAGP", "sources": ["packages/@react-aria/visually-hidden/src/VisuallyHidden.tsx"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {DOMAttributes} from '@react-types/shared';\nimport {mergeProps} from '@react-aria/utils';\nimport React, {CSSProperties, JSX, JSXElementConstructor, ReactNode, useMemo, useState} from 'react';\nimport {useFocusWithin} from '@react-aria/interactions';\n\nexport interface VisuallyHiddenProps extends DOMAttributes {\n  /** The content to visually hide. */\n  children?: ReactNode,\n\n  /**\n   * The element type for the container.\n   * @default 'div'\n   */\n  elementType?: string | JSXElementConstructor<any>,\n\n  /** Whether the element should become visible on focus, for example skip links. */\n  isFocusable?: boolean\n}\n\nconst styles: CSSProperties = {\n  border: 0,\n  clip: 'rect(0 0 0 0)',\n  clipPath: 'inset(50%)',\n  height: '1px',\n  margin: '-1px',\n  overflow: 'hidden',\n  padding: 0,\n  position: 'absolute',\n  width: '1px',\n  whiteSpace: 'nowrap'\n};\n\nexport interface VisuallyHiddenAria {\n  visuallyHiddenProps: DOMAttributes\n}\n\n/**\n * Provides props for an element that hides its children visually\n * but keeps content visible to assistive technology.\n */\nexport function useVisuallyHidden(props: VisuallyHiddenProps = {}): VisuallyHiddenAria {\n  let {\n    style,\n    isFocusable\n  } = props;\n\n  let [isFocused, setFocused] = useState(false);\n  let {focusWithinProps} = useFocusWithin({\n    isDisabled: !isFocusable,\n    onFocusWithinChange: (val) => setFocused(val)\n  });\n\n  // If focused, don't hide the element.\n  let combinedStyles = useMemo(() => {\n    if (isFocused) {\n      return style;\n    } else if (style) {\n      return {...styles, ...style};\n    } else {\n      return styles;\n    }\n  // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [isFocused]);\n\n  return {\n    visuallyHiddenProps: {\n      ...focusWithinProps,\n      style: combinedStyles\n    }\n  };\n}\n\n/**\n * VisuallyHidden hides its children visually, while keeping content visible\n * to screen readers.\n */\nexport function VisuallyHidden(props: VisuallyHiddenProps): JSX.Element {\n  // eslint-disable-next-line @typescript-eslint/no-unused-vars\n  let {children, elementType: Element = 'div', isFocusable, style, ...otherProps} = props;\n  let {visuallyHiddenProps} = useVisuallyHidden(props);\n\n  return (\n    <Element {...mergeProps(otherProps, visuallyHiddenProps)}>\n      {children}\n    </Element>\n  );\n}\n"], "names": [], "version": 3, "file": "VisuallyHidden.main.js.map"}