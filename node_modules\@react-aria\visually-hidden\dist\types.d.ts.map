{"mappings": ";;AAiBA,oCAAqC,SAAQ,aAAa;IACxD,oCAAoC;IACpC,QAAQ,CAAC,EAAE,SAAS,CAAC;IAErB;;;OAGG;IACH,WAAW,CAAC,EAAE,MAAM,GAAG,sBAAsB,GAAG,CAAC,CAAC;IAElD,kFAAkF;IAClF,WAAW,CAAC,EAAE,OAAO,CAAA;CACtB;AAeD;IACE,mBAAmB,EAAE,aAAa,CAAA;CACnC;AAED;;;GAGG;AACH,kCAAkC,KAAK,GAAE,mBAAwB,GAAG,kBAAkB,CA8BrF;AAED;;;GAGG;AACH,+BAA+B,KAAK,EAAE,mBAAmB,GAAG,IAAI,OAAO,CAUtE", "sources": ["packages/@react-aria/visually-hidden/src/packages/@react-aria/visually-hidden/src/VisuallyHidden.tsx", "packages/@react-aria/visually-hidden/src/packages/@react-aria/visually-hidden/src/index.ts", "packages/@react-aria/visually-hidden/src/index.ts"], "sourcesContent": [null, null, "/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\nexport {useVisuallyHidden, VisuallyHidden} from './VisuallyHidden';\nexport type {VisuallyHiddenAria, VisuallyHiddenProps} from './VisuallyHidden';\n"], "names": [], "version": 3, "file": "types.d.ts.map"}