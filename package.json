{"name": "v2board-dashboard", "version": "1.0.0", "description": "V2Board Dashboard with Node.js and HeroUI", "main": "index.js", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "serve": "node server.js"}, "keywords": ["v2board", "dashboard", "<PERSON><PERSON>", "react", "vite"], "author": "", "license": "MIT", "dependencies": {"@heroui/react": "^2.4.6", "@heroui/theme": "^2.2.9", "axios": "^1.7.7", "chart.js": "^4.4.4", "clsx": "^2.1.1", "framer-motion": "^11.5.4", "lucide-react": "^0.441.0", "react": "^18.2.0", "react-chartjs-2": "^5.2.0", "react-dom": "^18.2.0", "react-hot-toast": "^2.5.2", "react-router-dom": "^6.26.1", "tailwind-merge": "^2.5.2"}, "devDependencies": {"@types/react": "^18.2.66", "@types/react-dom": "^18.2.22", "@vitejs/plugin-react": "^4.2.1", "autoprefixer": "^10.4.19", "postcss": "^8.4.38", "tailwindcss": "^3.4.4", "vite": "^5.2.0"}}