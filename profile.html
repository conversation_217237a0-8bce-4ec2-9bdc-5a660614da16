<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>我的账户 - V2Board Dashboard</title>
    <!-- 引入 Tailwind CSS for styling -->
    <script src="https://cdn.tailwindcss.com"></script>
    <!-- 引入 Ionicons for icons -->
    <script type="module" src="https://unpkg.com/ionicons@5.5.2/dist/ionicons/ionicons.esm.js"></script>
    <script nomodule src="https://unpkg.com/ionicons@5.5.2/dist/ionicons/ionicons.js"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <style>
        /* Custom Styles - Consistent with dashboard */
        body {
            font-family: 'Inter', sans-serif;
            background-color: #f3f4f6;
        }
        .card {
            background-color: white;
            border-radius: 1rem;
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
        }
        .list-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 1rem 0;
            border-bottom: 1px solid #e5e7eb;
        }
        .list-item:last-child {
            border-bottom: none;
        }
        .list-item-label {
            color: #6b7280; /* Gray-500 */
            font-weight: 500;
        }
        .list-item-value {
            color: #1f2937; /* Gray-800 */
            font-weight: 600;
        }
        .status-badge {
            display: inline-block;
            padding: 0.25rem 0.75rem;
            border-radius: 9999px;
            font-size: 0.875rem;
            font-weight: 600;
        }
        .status-badge.success {
            background-color: #dcfce7; /* Green-100 */
            color: #166534; /* Green-800 */
        }
        .status-badge.danger {
            background-color: #fee2e2; /* Red-100 */
            color: #991b1b; /* Red-800 */
        }
        .toast {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 1rem 1.5rem;
            border-radius: 0.75rem;
            color: white;
            z-index: 100;
            opacity: 0;
            transition: opacity 0.3s ease-in-out, transform 0.3s ease-in-out;
            transform: translateY(-20px);
        }
        .toast.show {
            opacity: 1;
            transform: translateY(0);
        }
        .toast.error { background-color: #ef4444; }
        .toast.success { background-color: #10b981; }
        
        /* Modal for Gemini Help */
        .modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: rgba(0, 0, 0, 0.5);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 50;
            opacity: 0;
            visibility: hidden;
            transition: opacity 0.3s, visibility 0.3s;
        }
        .modal-overlay.show {
            opacity: 1;
            visibility: visible;
        }
        .modal-content {
            background-color: white;
            padding: 2rem;
            border-radius: 1rem;
            max-width: 90%;
            width: 500px;
            box-shadow: 0 20px 25px -5px rgba(0,0,0,0.1), 0 10px 10px -5px rgba(0,0,0,0.04);
            transform: scale(0.95);
            transition: transform 0.3s;
        }
        .modal-overlay.show .modal-content {
            transform: scale(1);
        }
        .btn {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            padding: 0.5rem 1rem;
            border-radius: 0.5rem;
            font-weight: 600;
            transition: all 0.2s ease-in-out;
            cursor: pointer;
            border: 1px solid transparent;
        }
        .btn-primary {
            background-color: #4f46e5;
            color: white;
        }
        .btn-primary:hover {
            background-color: #4338ca;
        }
        .btn-secondary {
            background-color: #e5e7eb;
            color: #1f2937;
        }
        .btn-secondary:hover {
            background-color: #d1d5db;
        }
        /* Spinner for AI buttons */
        .ai-spinner {
            animation: spin 1s linear infinite;
            display: inline-block;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body class="antialiased">
    <div id="toast-container" class="toast"></div>

    <div class="flex h-screen bg-gray-100">
        <!-- Sidebar -->
        <div class="hidden md:flex flex-col w-64 bg-white shadow-lg">
            <div class="flex items-center justify-center h-20 border-b">
                <h1 class="text-2xl font-bold text-indigo-600">My Dashboard</h1>
            </div>
            <div class="flex-grow p-4">
                <nav>
                    <a href="index.html" class="flex items-center px-4 py-3 text-gray-600 rounded-lg hover:bg-gray-200">
                        <ion-icon name="home-outline" class="text-xl"></ion-icon>
                        <span class="mx-4 font-medium">仪表盘</span>
                    </a>
                    <a href="#" class="flex items-center px-4 py-3 mt-4 text-gray-600 rounded-lg hover:bg-gray-200">
                        <ion-icon name="server-outline" class="text-xl"></ion-icon>
                        <span class="mx-4 font-medium">节点列表</span>
                    </a>
                    <a href="#" class="flex items-center px-4 py-3 mt-4 text-gray-600 rounded-lg hover:bg-gray-200">
                        <ion-icon name="cart-outline" class="text-xl"></ion-icon>
                        <span class="mx-4 font-medium">购买订阅</span>
                    </a>
                    <a href="#" class="flex items-center px-4 py-3 mt-4 text-white bg-indigo-600 rounded-lg">
                        <ion-icon name="person-outline" class="text-xl"></ion-icon>
                        <span class="mx-4 font-medium">我的账户</span>
                    </a>
                </nav>
            </div>
        </div>

        <!-- Main content -->
        <div class="flex flex-col flex-1 overflow-y-auto">
            <header class="flex items-center justify-between h-20 px-6 bg-white border-b">
                 <h2 class="text-2xl font-semibold text-gray-800">我的账户</h2>
                <div id="user-header" class="flex items-center">
                   <!-- User info here will be populated by JS -->
                </div>
            </header>
            
            <main class="flex-1 p-6 md:p-10">
                <!-- Loading Spinner -->
                <div id="loading-spinner" class="text-center py-10">
                    <svg class="animate-spin h-10 w-10 text-indigo-600 mx-auto" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    <p class="mt-4 text-gray-600">正在加载账户信息...</p>
                </div>

                <!-- Account Content -->
                <div id="account-content" class="hidden grid grid-cols-1 lg:grid-cols-3 gap-8">
                    <!-- Left Column: Profile Card -->
                    <div class="lg:col-span-1 space-y-8">
                        <div class="card p-6 text-center">
                            <img id="avatar" src="https://placehold.co/128x128/e0e0e0/757575?text=... " class="w-32 h-32 rounded-full mx-auto mb-4" alt="用户头像">
                            <h3 id="email-profile" class="text-2xl font-bold text-gray-900">...</h3>
                            <p class="text-gray-500 mt-1">UUID: <span id="uuid">...</span></p>
                        </div>
                        <!-- Gemini AI Assistant Card -->
                        <div class="card p-6">
                            <div class="flex items-center mb-4">
                                <ion-icon name="sparkles-outline" class="text-2xl text-yellow-500"></ion-icon>
                                <h4 class="text-xl font-semibold ml-2 text-gray-800">AI 助手</h4>
                            </div>
                            <p class="text-gray-600 mb-4 text-sm">让 AI 为您生成一段简明扼要的账户小结。</p>
                            <button id="generate-summary-btn" class="btn btn-primary w-full">
                                <span class="btn-text">✨ 生成账户小结</span>
                            </button>
                            <div id="summary-output-container" class="mt-4 p-4 bg-gray-50 rounded-lg text-gray-700 text-sm hidden leading-relaxed">
                                <p id="summary-output"></p>
                            </div>
                        </div>
                    </div>

                    <!-- Right Column: Details -->
                    <div class="lg:col-span-2 space-y-8">
                        <!-- Account Details Card -->
                        <div class="card p-6">
                            <h4 class="text-xl font-semibold mb-4 text-gray-800">账户详情</h4>
                            <div class="divide-y divide-gray-200">
                                <div class="list-item">
                                    <span class="list-item-label">账户状态</span>
                                    <span id="account-status" class="list-item-value">...</span>
                                </div>
                                <div class="list-item">
                                    <span class="list-item-label">当前套餐ID</span>
                                    <span id="plan-id" class="list-item-value">...</span>
                                </div>
                                <div class="list-item">
                                    <span class="list-item-label">套餐到期</span>
                                    <span id="expired-at" class="list-item-value">...</span>
                                </div>
                                <div class="list-item">
                                    <span class="list-item-label">注册时间</span>
                                    <span id="created-at" class="list-item-value">...</span>
                                </div>
                                <div class="list-item">
                                    <span class="list-item-label">上次登录</span>
                                    <span id="last-login-at" class="list-item-value">...</span>
                                </div>
                            </div>
                        </div>

                        <!-- Financial Details Card -->
                        <div class="card p-6">
                            <h4 class="text-xl font-semibold mb-4 text-gray-800">财务信息</h4>
                             <div class="divide-y divide-gray-200">
                                <div class="list-item">
                                    <span class="list-item-label">账户余额</span>
                                    <span id="balance" class="list-item-value">...</span>
                                </div>
                                <div class="list-item">
                                    <span class="list-item-label">佣金余额</span>
                                    <span id="commission-balance" class="list-item-value">...</span>
                                </div>
                                <div class="list-item">
                                    <span class="list-item-label">推广佣金比例</span>
                                    <span id="commission-rate" class="list-item-value">...</span>
                                </div>
                            </div>
                        </div>
                         <!-- Settings Card -->
                        <div class="card p-6">
                            <h4 class="text-xl font-semibold mb-4 text-gray-800">通知与绑定</h4>
                            <div class="divide-y divide-gray-200">
                                <div class="list-item">
                                    <span class="list-item-label">到期邮件提醒</span>
                                    <span id="remind-expire" class="list-item-value">...</span>
                                </div>
                                <div class="list-item">
                                    <span class="list-item-label">流量邮件提醒</span>
                                    <span id="remind-traffic" class="list-item-value">...</span>
                                </div>
                                <div class="list-item">
                                    <span class="list-item-label">Telegram ID</span>
                                    <div class="flex items-center">
                                       <span id="telegram-id" class="list-item-value mr-2">...</span>
                                       <button id="telegram-help-btn" class="text-indigo-600 hover:text-indigo-800 text-xs focus:outline-none">
                                          ✨ 如何绑定?
                                       </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <!-- Gemini Help Modal -->
    <div id="help-modal" class="modal-overlay">
        <div class="modal-content">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-xl font-bold">AI 绑定助手</h3>
                <button id="close-modal-btn" class="text-gray-500 hover:text-gray-800">
                    <ion-icon name="close-outline" class="text-2xl"></ion-icon>
                </button>
            </div>
            <div id="modal-body" class="text-gray-700 leading-relaxed">
                <!-- Gemini content will be injected here -->
                <div id="modal-spinner" class="text-center p-8">
                     <svg class="animate-spin h-8 w-8 text-indigo-600 mx-auto" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    <p class="mt-4 text-sm">正在请求 AI 助手获取帮助...</p>
                </div>
                 <div id="modal-text-content" class="prose max-w-none"></div>
            </div>
        </div>
    </div>


    <script>
        document.addEventListener('DOMContentLoaded', async function() {
            // --- DOM Elements ---
            const loadingSpinner = document.getElementById('loading-spinner');
            const accountContent = document.getElementById('account-content');
            const generateSummaryBtn = document.getElementById('generate-summary-btn');
            const summaryOutputContainer = document.getElementById('summary-output-container');
            const summaryOutput = document.getElementById('summary-output');
            const telegramHelpBtn = document.getElementById('telegram-help-btn');
            const helpModal = document.getElementById('help-modal');
            const closeModalBtn = document.getElementById('close-modal-btn');
            const modalBody = document.getElementById('modal-body');
            const modalSpinner = document.getElementById('modal-spinner');
            const modalTextContent = document.getElementById('modal-text-content');
            
            let userInfo = null; // Store user info globally in this scope

            // --- Get Auth Info from Local Storage ---
            const baseUrl = localStorage.getItem('v2board_url');
            const authToken = localStorage.getItem('v2board_auth_token'); 

            if (!baseUrl || !authToken) {
                showToast('请先在主页登录', 'error');
                loadingSpinner.innerHTML = '<p class="text-red-500">认证失败，请返回主页登录。</p>';
                return;
            }

            try {
                userInfo = await fetchApi('/api/v1/user/info', baseUrl, authToken);
                if (userInfo) {
                    updateAccountUI(userInfo);
                    loadingSpinner.classList.add('hidden');
                    accountContent.classList.remove('hidden');
                }
            } catch (error) {
                 showToast(`加载信息失败: ${error.message}`, 'error');
                 loadingSpinner.innerHTML = `<p class="text-red-500">加载信息失败: ${error.message}</p>`;
            }

            // --- Gemini API Call ---
            /**
            * Calls the Gemini API to generate text.
            * @param {string} prompt - The prompt to send to the model.
            * @returns {Promise<string>} The generated text.
            */
            async function callGemini(prompt) {
                const apiKey = ""; // Canvas will provide the key
                const apiUrl = `https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent?key=${apiKey}`;

                const payload = {
                    contents: [{ role: "user", parts: [{ text: prompt }] }],
                };

                try {
                    const response = await fetch(apiUrl, {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify(payload),
                    });

                    if (!response.ok) {
                        const errorBody = await response.json();
                        throw new Error(`API Error: ${errorBody.error.message}`);
                    }

                    const result = await response.json();
                    if (result.candidates && result.candidates[0].content.parts[0].text) {
                        return result.candidates[0].content.parts[0].text;
                    } else {
                        throw new Error("未能从 AI 获取有效回复。");
                    }
                } catch (error) {
                    console.error("Gemini API call failed:", error);
                    throw error;
                }
            }

            // --- Event Listeners for AI features ---
            generateSummaryBtn.addEventListener('click', async () => {
                if (!userInfo) {
                    showToast('用户数据未加载，无法生成小结。', 'error');
                    return;
                }
                
                const btnText = generateSummaryBtn.querySelector('.btn-text');
                btnText.innerHTML = '<ion-icon name="sync-outline" class="ai-spinner"></ion-icon> AI 正在思考...';
                generateSummaryBtn.disabled = true;

                try {
                    const prompt = `您是一位友好的客户服务助手。请根据以下 JSON 数据，为用户生成一段亲切的中文账户小结。
                    数据:
                    - 账户状态 (banned): ${userInfo.banned}
                    - 套餐ID (plan_id): ${userInfo.plan_id}
                    - 套餐到期时间 (expired_at, unix timestamp): ${userInfo.expired_at}
                    - 账户余额 (balance, in cents): ${userInfo.balance}
                    - 注册时间 (created_at, unix timestamp): ${userInfo.created_at}

                    小结应包含：
                    1. 一句问候。
                    2. 账户是否正常。
                    3. 当前套餐的到期日期（格式化为 YYYY-MM-DD）。
                    4. 账户余额（格式化为 ¥XX.XX）。
                    5. 一句鼓励或提醒的话。
                    
                    请以自然、对话式的语气呈现。`;

                    const summary = await callGemini(prompt);
                    summaryOutput.innerHTML = summary.replace(/\n/g, '<br>');
                    summaryOutputContainer.classList.remove('hidden');

                } catch (error) {
                    showToast(`AI 小结生成失败: ${error.message}`, 'error');
                } finally {
                    btnText.innerHTML = '✨ 生成账户小结';
                    generateSummaryBtn.disabled = false;
                }
            });
            
            telegramHelpBtn.addEventListener('click', async () => {
                helpModal.classList.add('show');
                modalTextContent.classList.add('hidden');
                modalSpinner.classList.remove('hidden');

                try {
                    const prompt = `请为一位非技术背景的用户提供一份关于如何在 Telegram 上找到自己 User ID 的详细中文指南。
                    指南需要包括以下步骤：
                    1. 解释需要使用一个特定的机器人来获取 ID。
                    2. 推荐一个常用的机器人，例如 @userinfobot。
                    3. 指导用户如何在 Telegram 中搜索并找到这个机器人。
                    4. 指导用户如何与机器人开始对话（点击 "Start" 或发送 "/start"）。
                    5. 说明机器人会自动回复用户的 User ID，并高亮显示 "ID" 这个关键信息。
                    6. 简单提醒用户复制这个 ID 并粘贴到我们网站的相应位置。
                    
                    请使用清晰、简洁的语言，可以适当使用表情符号来增加亲和力。`;
                    
                    const helpText = await callGemini(prompt);
                    modalTextContent.innerHTML = helpText.replace(/\n/g, '<br>');
                    modalSpinner.classList.add('hidden');
                    modalTextContent.classList.remove('hidden');

                } catch(error) {
                    modalSpinner.classList.add('hidden');
                    modalTextContent.innerHTML = `<p class="text-red-500">无法获取帮助信息: ${error.message}</p>`;
                    modalTextContent.classList.remove('hidden');
                }
            });

            closeModalBtn.addEventListener('click', () => helpModal.classList.remove('show'));
            helpModal.addEventListener('click', (e) => {
                if (e.target === helpModal) {
                    helpModal.classList.remove('show');
                }
            });


            /**
             * Generic fetch wrapper for the V2Board API.
             */
            async function fetchApi(endpoint, baseUrl, token) {
                const url = new URL(endpoint, baseUrl).href;
                const response = await fetch(url, {
                    headers: { 'Authorization': token }
                });
                if (!response.ok) {
                    throw new Error(`API 请求失败，状态: ${response.status}`);
                }
                const result = await response.json();
                if (result.data) {
                    return result.data;
                } else {
                    throw new Error(result.message || 'API 返回数据格式不正确');
                }
            }
            
            /**
             * Updates the account page UI with fetched data.
             */
            function updateAccountUI(data) {
                // --- Header ---
                document.getElementById('user-header').innerHTML = `
                    <span class="text-right">${data.email}</span>
                    <img class="w-8 h-8 ml-2 rounded-full object-cover" src="${data.avatar_url}" alt="用户头像" onerror="this.src='https://placehold.co/100x100/663399/FFFFFF?text=U'">
                `;

                // --- Profile Card ---
                document.getElementById('avatar').src = data.avatar_url;
                document.getElementById('avatar').onerror = () => { document.getElementById('avatar').src = 'https://placehold.co/128x128/e0e0e0/757575?text=... ' };
                document.getElementById('email-profile').textContent = data.email;
                document.getElementById('uuid').textContent = data.uuid;

                // --- Account Details ---
                const statusBadge = data.banned 
                    ? '<span class="status-badge danger">已封禁</span>'
                    : '<span class="status-badge success">正常</span>';
                document.getElementById('account-status').innerHTML = statusBadge;

                document.getElementById('plan-id').textContent = data.plan_id || '无';
                document.getElementById('expired-at').textContent = data.expired_at ? formatTimestamp(data.expired_at) : '长期有效';
                document.getElementById('created-at').textContent = formatTimestamp(data.created_at);
                document.getElementById('last-login-at').textContent = data.last_login_at ? formatTimestamp(data.last_login_at) : '从未';
                
                // --- Financial Details ---
                document.getElementById('balance').textContent = `¥${(data.balance / 100).toFixed(2)}`;
                document.getElementById('commission-balance').textContent = `¥${(data.commission_balance / 100).toFixed(2)}`;
                document.getElementById('commission-rate').textContent = data.commission_rate !== null ? `${data.commission_rate}%` : '未设置';

                // --- Settings ---
                document.getElementById('remind-expire').textContent = data.remind_expire ? '是' : '否';
                document.getElementById('remind-traffic').textContent = data.remind_traffic ? '是' : '否';
                document.getElementById('telegram-id').textContent = data.telegram_id || '未绑定';

            }

            /**
             * Formats a Unix timestamp into a readable date string.
             */
            function formatTimestamp(timestamp) {
                if (!timestamp) return 'N/A';
                return new Date(timestamp * 1000).toLocaleString('zh-CN', {
                    year: 'numeric',
                    month: '2-digit',
                    day: '2-digit',
                    hour: '2-digit',
                    minute: '2-digit'
                });
            }

            /**
             * Shows a toast notification.
             */
            function showToast(message, type = 'error') {
                const toast = document.getElementById('toast-container');
                toast.textContent = message;
                toast.className = `toast ${type}`;
                setTimeout(() => { toast.classList.add('show'); }, 10);
                setTimeout(() => { toast.classList.remove('show'); }, 3000);
            }
        });
    </script>
</body>
</html>
