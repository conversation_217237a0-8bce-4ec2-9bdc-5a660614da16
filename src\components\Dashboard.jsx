import React, { useState, useEffect } from 'react'
import { 
  Card, 
  CardBody, 
  CardHeader,
  Progress,
  Button,
  Chip,
  Divider,
  Spinner
} from '@heroui/react'
import {
  RocketIcon,
  ClockIcon,
  RefreshCwIcon,
  CopyIcon,
  DownloadIcon,
  BookOpenIcon,
  StoreIcon,
  TrendingUpIcon,
  ServerIcon,
  CalendarIcon
} from 'lucide-react'
import { useUser } from '../contexts/UserContext'
import V2BoardAPI, { formatBytes, formatDate, calculateDaysLeft, calculateUsagePercentage } from '../services/api'
import toast from 'react-hot-toast'
import TrafficChart from './TrafficChart'

const Dashboard = () => {
  const { user } = useUser()
  const [dashboardData, setDashboardData] = useState(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)

  useEffect(() => {
    if (user) {
      fetchDashboardData()
    }
  }, [user])

  const fetchDashboardData = async () => {
    if (!user) return

    setLoading(true)
    setError(null)

    try {
      const api = new V2BoardAPI(user.baseUrl, user.token)
      const data = await api.getSubscribe()
      setDashboardData(data)
    } catch (error) {
      setError(error.message)
      toast.error(`加载数据失败: ${error.message}`)
    } finally {
      setLoading(false)
    }
  }

  const copySubscriptionLink = () => {
    if (dashboardData?.subscribe_url) {
      navigator.clipboard.writeText(dashboardData.subscribe_url)
      toast.success('订阅链接已复制到剪贴板！')
    } else {
      toast.error('没有可复制的订阅链接')
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <Spinner size="lg" color="primary" />
      </div>
    )
  }

  if (error) {
    return (
      <div className="text-center py-12">
        <p className="text-danger mb-4">{error}</p>
        <Button color="primary" onClick={fetchDashboardData}>
          重试
        </Button>
      </div>
    )
  }

  if (!dashboardData) {
    return (
      <div className="text-center py-12">
        <p className="text-gray-600 dark:text-gray-400">暂无数据</p>
      </div>
    )
  }

  const { plan, expired_at, reset_day, transfer_enable, d, u, subscribe_url } = dashboardData
  const usedTraffic = d + u
  const remainingTraffic = transfer_enable - usedTraffic
  const usagePercentage = calculateUsagePercentage(usedTraffic, transfer_enable)

  return (
    <div className="space-y-6 animate-fade-in">
      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {/* Subscription Info */}
        <Card className="hover:shadow-lg transition-shadow">
          <CardBody className="p-6">
            <div className="flex items-center mb-4">
              <div className="p-3 bg-primary-100 dark:bg-primary-900 rounded-full">
                <RocketIcon className="w-6 h-6 text-primary-600 dark:text-primary-400" />
              </div>
              <div className="ml-4">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                  我的订阅
                </h3>
              </div>
            </div>
            <p className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
              {plan?.name || '无订阅'}
            </p>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              到期时间: {formatDate(expired_at)}
            </p>
          </CardBody>
        </Card>

        {/* Days Left */}
        <Card className="hover:shadow-lg transition-shadow">
          <CardBody className="p-6">
            <div className="flex items-center mb-4">
              <div className="p-3 bg-success-100 dark:bg-success-900 rounded-full">
                <ClockIcon className="w-6 h-6 text-success-600 dark:text-success-400" />
              </div>
              <div className="ml-4">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                  剩余时间
                </h3>
              </div>
            </div>
            <p className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
              {calculateDaysLeft(expired_at)}
            </p>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              剩余可用天数
            </p>
          </CardBody>
        </Card>

        {/* Reset Day */}
        <Card className="hover:shadow-lg transition-shadow">
          <CardBody className="p-6">
            <div className="flex items-center mb-4">
              <div className="p-3 bg-warning-100 dark:bg-warning-900 rounded-full">
                <RefreshCwIcon className="w-6 h-6 text-warning-600 dark:text-warning-400" />
              </div>
              <div className="ml-4">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                  流量重置日
                </h3>
              </div>
            </div>
            <p className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
              {plan?.reset_traffic_method === null ? `每月 ${reset_day} 日` : 'N/A'}
            </p>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              {plan?.reset_traffic_method === null ? '每月流量重置日' : '非按月重置套餐'}
            </p>
          </CardBody>
        </Card>
      </div>

      {/* Traffic Usage */}
      <Card className="hover:shadow-lg transition-shadow">
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between w-full">
            <h3 className="text-xl font-semibold text-gray-900 dark:text-white">
              流量使用情况
            </h3>
            <Chip 
              color={usagePercentage > 80 ? "danger" : usagePercentage > 60 ? "warning" : "success"}
              variant="flat"
            >
              {usagePercentage.toFixed(1)}% 已使用
            </Chip>
          </div>
        </CardHeader>
        <CardBody className="pt-0">
          <div className="space-y-4">
            <div className="flex justify-between text-sm text-gray-600 dark:text-gray-400">
              <span>已用 <strong>{formatBytes(usedTraffic)}</strong></span>
              <span>总计 <strong>{formatBytes(transfer_enable)}</strong></span>
            </div>
            
            <Progress 
              value={usagePercentage} 
              color={usagePercentage > 80 ? "danger" : usagePercentage > 60 ? "warning" : "success"}
              className="w-full"
              size="lg"
            />
            
            <div className="text-right text-sm text-gray-600 dark:text-gray-400">
              剩余流量: <strong>{formatBytes(remainingTraffic)}</strong>
            </div>
          </div>
        </CardBody>
      </Card>

      {/* Traffic Chart */}
      <TrafficChart />

      {/* Quick Actions */}
      <Card className="hover:shadow-lg transition-shadow">
        <CardHeader>
          <h3 className="text-xl font-semibold text-gray-900 dark:text-white">
            快捷操作
          </h3>
        </CardHeader>
        <CardBody>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <Button
              color="primary"
              startContent={<CopyIcon className="w-4 h-4" />}
              onClick={copySubscriptionLink}
              className="h-12"
            >
              复制订阅链接
            </Button>
            
            <Button
              color="default"
              variant="bordered"
              startContent={<DownloadIcon className="w-4 h-4" />}
              className="h-12"
            >
              客户端下载
            </Button>
            
            <Button
              color="default"
              variant="bordered"
              startContent={<BookOpenIcon className="w-4 h-4" />}
              className="h-12"
            >
              使用文档
            </Button>
            
            <Button
              color="default"
              variant="bordered"
              startContent={<StoreIcon className="w-4 h-4" />}
              className="h-12"
            >
              应用商店
            </Button>
          </div>
        </CardBody>
      </Card>
    </div>
  )
}

export default Dashboard
