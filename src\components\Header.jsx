import React from 'react'
import {
  Navbar,
  NavbarBrand,
  NavbarContent,
  NavbarItem,
  Button,
  Dropdown,
  DropdownTrigger,
  DropdownMenu,
  DropdownItem,
  Avatar
} from '@heroui/react'
import {
  MenuIcon,
  SunIcon,
  MoonIcon,
  LogOutIcon,
  UserIcon
} from 'lucide-react'
import { useUser } from '../contexts/UserContext'
import NotificationPanel from './NotificationPanel'

const Header = ({ onMenuClick, onLogout }) => {
  const { user } = useUser()
  const [isDark, setIsDark] = React.useState(() => {
    return document.documentElement.classList.contains('dark')
  })

  const toggleTheme = () => {
    const newTheme = !isDark
    setIsDark(newTheme)
    document.documentElement.classList.toggle('dark', newTheme)
    localStorage.setItem('theme', newTheme ? 'dark' : 'light')
  }

  // 初始化主题
  React.useEffect(() => {
    const savedTheme = localStorage.getItem('theme')
    const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches
    const shouldBeDark = savedTheme === 'dark' || (!savedTheme && prefersDark)

    setIsDark(shouldBeDark)
    document.documentElement.classList.toggle('dark', shouldBeDark)
  }, [])

  return (
    <Navbar
      isBordered
      maxWidth="full"
      className="bg-white/80 dark:bg-gray-900/80 backdrop-blur-md border-b border-gray-200/50 dark:border-gray-700/50"
    >
      <NavbarContent justify="start" className="gap-4">
        {/* 移动端菜单按钮 */}
        <NavbarItem className="lg:hidden">
          <Button
            isIconOnly
            variant="light"
            size="sm"
            onClick={onMenuClick}
            className="text-gray-600 dark:text-gray-300"
          >
            <MenuIcon className="w-5 h-5" />
          </Button>
        </NavbarItem>

        {/* 桌面端欢迎信息 */}
        <NavbarBrand className="hidden lg:flex">
          <div className="flex items-center gap-3">
            <div className="w-8 h-8 bg-gradient-to-br from-primary-500 to-primary-600 rounded-lg flex items-center justify-center">
              <span className="text-white font-bold text-sm">V2</span>
            </div>
            <div>
              <h1 className="text-lg font-bold text-gray-900 dark:text-white">
                V2Board
              </h1>
              <p className="text-xs text-gray-500 dark:text-gray-400 -mt-1">
                {user?.email || '未登录'}
              </p>
            </div>
          </div>
        </NavbarBrand>
      </NavbarContent>

      <NavbarContent justify="end" className="gap-2">
        {/* 主题切换 */}
        <NavbarItem>
          <Button
            isIconOnly
            variant="light"
            size="sm"
            onClick={toggleTheme}
            className="text-gray-600 dark:text-gray-300"
          >
            {isDark ? (
              <SunIcon className="w-4 h-4" />
            ) : (
              <MoonIcon className="w-4 h-4" />
            )}
          </Button>
        </NavbarItem>

        {/* 通知 */}
        <NavbarItem>
          <NotificationPanel />
        </NavbarItem>

        {/* 用户菜单 */}
        <NavbarItem>
          <Dropdown placement="bottom-end">
            <DropdownTrigger>
              <Avatar
                as="button"
                className="transition-transform hover:scale-105"
                color="primary"
                name={user?.email?.charAt(0).toUpperCase() || 'U'}
                size="sm"
                isBordered
                src=""
              />
            </DropdownTrigger>
            <DropdownMenu
              aria-label="Profile Actions"
              variant="faded"
              className="w-64"
            >
              <DropdownItem key="profile" className="h-14 gap-2" textValue="用户信息">
                <div className="flex flex-col">
                  <p className="font-semibold text-sm">当前用户</p>
                  <p className="text-xs text-gray-500 truncate">{user?.email || '未登录'}</p>
                </div>
              </DropdownItem>
              <DropdownItem
                key="settings"
                startContent={<UserIcon className="w-4 h-4" />}
                className="text-sm"
              >
                个人设置
              </DropdownItem>
              <DropdownItem
                key="logout"
                color="danger"
                startContent={<LogOutIcon className="w-4 h-4" />}
                onClick={onLogout}
                className="text-sm"
              >
                退出登录
              </DropdownItem>
            </DropdownMenu>
          </Dropdown>
        </NavbarItem>
      </NavbarContent>
    </Navbar>
  )
}

export default Header
