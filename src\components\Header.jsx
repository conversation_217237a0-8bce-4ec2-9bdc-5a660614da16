import React from 'react'
import { 
  Navbar,
  Navbar<PERSON>rand,
  NavbarContent,
  NavbarItem,
  Button,
  Dropdown,
  DropdownTrigger,
  DropdownMenu,
  DropdownItem,
  Avatar,
  Badge
} from '@heroui/react'
import { 
  Bars3Icon, 
  BellIcon, 
  SunIcon, 
  MoonIcon,
  ArrowRightOnRectangleIcon,
  UserIcon
} from 'lucide-react'
import { useUser } from '../contexts/UserContext'

const Header = ({ onMenuClick, onLogout }) => {
  const { user } = useUser()
  const [isDark, setIsDark] = React.useState(false)

  const toggleTheme = () => {
    setIsDark(!isDark)
    document.documentElement.classList.toggle('dark')
  }

  return (
    <Navbar 
      isBordered 
      className="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700"
    >
      <NavbarContent justify="start">
        <NavbarItem>
          <Button
            isIconOnly
            variant="light"
            className="lg:hidden"
            onClick={onMenuClick}
          >
            <Bars3Icon className="w-6 h-6" />
          </Button>
        </NavbarItem>
        <NavbarBrand className="hidden lg:flex">
          <div className="ml-4">
            <h2 className="text-xl font-semibold text-gray-800 dark:text-white">
              欢迎回来, <span className="text-primary-600">{user?.email || '用户'}</span>
            </h2>
          </div>
        </NavbarBrand>
      </NavbarContent>

      <NavbarContent justify="end">
        <NavbarItem>
          <Button
            isIconOnly
            variant="light"
            onClick={toggleTheme}
          >
            {isDark ? (
              <SunIcon className="w-5 h-5" />
            ) : (
              <MoonIcon className="w-5 h-5" />
            )}
          </Button>
        </NavbarItem>
        
        <NavbarItem>
          <Badge content="5" color="danger" size="sm">
            <Button isIconOnly variant="light">
              <BellIcon className="w-5 h-5" />
            </Button>
          </Badge>
        </NavbarItem>

        <NavbarItem>
          <Dropdown placement="bottom-end">
            <DropdownTrigger>
              <Avatar
                as="button"
                className="transition-transform"
                color="primary"
                name={user?.email?.charAt(0).toUpperCase() || 'U'}
                size="sm"
                src=""
              />
            </DropdownTrigger>
            <DropdownMenu aria-label="Profile Actions" variant="flat">
              <DropdownItem key="profile" className="h-14 gap-2">
                <p className="font-semibold">登录身份</p>
                <p className="font-semibold">{user?.email}</p>
              </DropdownItem>
              <DropdownItem 
                key="settings" 
                startContent={<UserIcon className="w-4 h-4" />}
              >
                个人设置
              </DropdownItem>
              <DropdownItem 
                key="logout" 
                color="danger"
                startContent={<ArrowRightOnRectangleIcon className="w-4 h-4" />}
                onClick={onLogout}
              >
                退出登录
              </DropdownItem>
            </DropdownMenu>
          </Dropdown>
        </NavbarItem>
      </NavbarContent>
    </Navbar>
  )
}

export default Header
