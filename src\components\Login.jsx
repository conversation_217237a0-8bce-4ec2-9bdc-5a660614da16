import React, { useState, useEffect } from 'react'
import { 
  Card, 
  CardBody, 
  CardHeader, 
  Input, 
  Button, 
  Di<PERSON>r,
  Spinner
} from '@heroui/react'
import { EyeIcon, EyeSlashIcon, ServerIcon } from 'lucide-react'
import { useUser } from '../contexts/UserContext'
import toast from 'react-hot-toast'

const Login = () => {
  const [formData, setFormData] = useState({
    baseUrl: '',
    email: '',
    password: ''
  })
  const [showPassword, setShowPassword] = useState(false)
  const { login, loading, error } = useUser()

  // Load saved data from localStorage
  useEffect(() => {
    const savedUrl = localStorage.getItem('v2board_url') || ''
    const savedEmail = localStorage.getItem('v2board_email') || ''
    
    setFormData(prev => ({
      ...prev,
      baseUrl: savedUrl,
      email: savedEmail
    }))
  }, [])

  const handleInputChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))
  }

  const handleSubmit = async (e) => {
    e.preventDefault()
    
    const { baseUrl, email, password } = formData
    
    if (!baseUrl || !email || !password) {
      toast.error('请填写所有登录字段')
      return
    }

    try {
      await login(baseUrl.trim(), email.trim(), password.trim())
      toast.success('登录成功！')
    } catch (error) {
      toast.error(error.message)
    }
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800 p-4">
      <div className="w-full max-w-md">
        <Card className="shadow-2xl">
          <CardHeader className="flex flex-col gap-3 pb-6">
            <div className="flex items-center justify-center w-16 h-16 bg-primary-100 dark:bg-primary-900 rounded-full mx-auto">
              <ServerIcon className="w-8 h-8 text-primary-600 dark:text-primary-400" />
            </div>
            <div className="text-center">
              <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
                V2Board Dashboard
              </h1>
              <p className="text-gray-600 dark:text-gray-400 mt-2">
                请登录您的账户以继续
              </p>
            </div>
          </CardHeader>
          
          <Divider />
          
          <CardBody className="pt-6">
            <form onSubmit={handleSubmit} className="space-y-6">
              <Input
                type="url"
                label="V2Board 地址"
                placeholder="https://example.com"
                value={formData.baseUrl}
                onValueChange={(value) => handleInputChange('baseUrl', value)}
                variant="bordered"
                isRequired
                startContent={
                  <div className="pointer-events-none flex items-center">
                    <span className="text-default-400 text-small">🌐</span>
                  </div>
                }
              />
              
              <Input
                type="email"
                label="邮箱地址"
                placeholder="<EMAIL>"
                value={formData.email}
                onValueChange={(value) => handleInputChange('email', value)}
                variant="bordered"
                isRequired
                startContent={
                  <div className="pointer-events-none flex items-center">
                    <span className="text-default-400 text-small">@</span>
                  </div>
                }
              />
              
              <Input
                type={showPassword ? "text" : "password"}
                label="密码"
                placeholder="请输入您的密码"
                value={formData.password}
                onValueChange={(value) => handleInputChange('password', value)}
                variant="bordered"
                isRequired
                endContent={
                  <button
                    className="focus:outline-none"
                    type="button"
                    onClick={() => setShowPassword(!showPassword)}
                  >
                    {showPassword ? (
                      <EyeSlashIcon className="w-5 h-5 text-default-400" />
                    ) : (
                      <EyeIcon className="w-5 h-5 text-default-400" />
                    )}
                  </button>
                }
              />
              
              {error && (
                <div className="text-danger text-small text-center">
                  {error}
                </div>
              )}
              
              <Button
                type="submit"
                color="primary"
                size="lg"
                className="w-full font-semibold"
                isLoading={loading}
                spinner={<Spinner size="sm" color="white" />}
              >
                {loading ? '登录中...' : '登录'}
              </Button>
            </form>
            
            <div className="mt-6 text-center">
              <p className="text-small text-gray-600 dark:text-gray-400">
                使用您的 V2Board 账户凭据登录
              </p>
            </div>
          </CardBody>
        </Card>
      </div>
    </div>
  )
}

export default Login
