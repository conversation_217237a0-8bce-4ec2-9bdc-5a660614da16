import React, { useState, useEffect } from 'react'
import {
  Card,
  CardBody,
  CardHeader,
  Button,
  Chip,
  <PERSON><PERSON>r,
  <PERSON><PERSON>,
  <PERSON>dal,
  ModalContent,
  ModalHeader,
  ModalBody,
  ModalFooter,
  useDisclosure
} from '@heroui/react'
import { 
  BellIcon, 
  AlertCircleIcon, 
  InfoIcon, 
  RefreshCwIcon,
  EyeIcon,
  CalendarIcon
} from 'lucide-react'
import { useUser } from '../contexts/UserContext'
import V2BoardAPI from '../services/api'
import toast from 'react-hot-toast'

const NotificationPage = () => {
  const { user } = useUser()
  const [notices, setNotices] = useState([])
  const [loading, setLoading] = useState(true)
  const [selectedNotice, setSelectedNotice] = useState(null)
  const { isOpen, onOpen, onClose } = useDisclosure()

  useEffect(() => {
    if (user) {
      fetchNotices()
    }
  }, [user])

  const fetchNotices = async () => {
    if (!user) return

    setLoading(true)
    try {
      const api = new V2BoardAPI(user.baseUrl, user.token)
      const data = await api.getNotices()
      setNotices(data || [])
    } catch (error) {
      console.error('Failed to fetch notices:', error)
      toast.error('获取通知失败')
    } finally {
      setLoading(false)
    }
  }

  const formatDate = (timestamp) => {
    return new Date(timestamp * 1000).toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  const getNoticeIcon = (notice) => {
    const title = notice.title?.toLowerCase() || ''
    const content = notice.content?.toLowerCase() || ''
    
    if (title.includes('中断') || title.includes('故障') || content.includes('中断') || content.includes('故障')) {
      return <AlertCircleIcon className="w-5 h-5 text-warning-500" />
    }
    return <InfoIcon className="w-5 h-5 text-primary-500" />
  }

  const getNoticeType = (notice) => {
    const title = notice.title?.toLowerCase() || ''
    const content = notice.content?.toLowerCase() || ''
    
    if (title.includes('中断') || title.includes('故障') || content.includes('中断') || content.includes('故障')) {
      return { label: '系统通知', color: 'warning' }
    }
    if (title.includes('更新') || title.includes('升级') || content.includes('更新') || content.includes('升级')) {
      return { label: '更新公告', color: 'primary' }
    }
    if (title.includes('优惠') || title.includes('活动') || content.includes('优惠') || content.includes('活动')) {
      return { label: '活动公告', color: 'success' }
    }
    return { label: '一般公告', color: 'default' }
  }

  const openNoticeDetail = (notice) => {
    setSelectedNotice(notice)
    onOpen()
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <Spinner size="lg" color="primary" />
      </div>
    )
  }

  return (
    <div className="space-y-6 animate-fade-in">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <div className="p-3 bg-primary-100 dark:bg-primary-900 rounded-full">
            <BellIcon className="w-6 h-6 text-primary-600 dark:text-primary-400" />
          </div>
          <div>
            <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
              通知公告
            </h1>
            <p className="text-gray-600 dark:text-gray-400">
              查看系统公告和重要通知
            </p>
          </div>
        </div>
        <Button
          color="primary"
          variant="bordered"
          startContent={<RefreshCwIcon className="w-4 h-4" />}
          onClick={fetchNotices}
          isLoading={loading}
        >
          刷新
        </Button>
      </div>

      {/* Notices List */}
      {notices.length === 0 ? (
        <Card>
          <CardBody className="text-center py-12">
            <BellIcon className="w-16 h-16 text-gray-300 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-gray-600 dark:text-gray-400 mb-2">
              暂无通知
            </h3>
            <p className="text-gray-500 dark:text-gray-500">
              目前没有任何系统公告或通知
            </p>
          </CardBody>
        </Card>
      ) : (
        <div className="space-y-4">
          {notices.map((notice, index) => {
            const noticeType = getNoticeType(notice)
            return (
              <Card 
                key={notice.id || index} 
                className="hover:shadow-lg transition-shadow cursor-pointer"
                isPressable
                onPress={() => openNoticeDetail(notice)}
              >
                <CardBody className="p-6">
                  <div className="flex gap-4">
                    <div className="flex-shrink-0 mt-1">
                      {getNoticeIcon(notice)}
                    </div>
                    <div className="flex-1 min-w-0">
                      <div className="flex items-start justify-between gap-4 mb-3">
                        <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                          {notice.title}
                        </h3>
                        <div className="flex items-center gap-2 flex-shrink-0">
                          <Chip 
                            size="sm" 
                            color={noticeType.color} 
                            variant="flat"
                          >
                            {noticeType.label}
                          </Chip>
                          {!notice.show && (
                            <Chip size="sm" color="default" variant="flat">
                              已隐藏
                            </Chip>
                          )}
                        </div>
                      </div>
                      
                      <p className="text-gray-600 dark:text-gray-400 mb-4 line-clamp-3">
                        {notice.content}
                      </p>
                      
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-4 text-sm text-gray-500">
                          <div className="flex items-center gap-1">
                            <CalendarIcon className="w-4 h-4" />
                            <span>发布时间: {formatDate(notice.created_at)}</span>
                          </div>
                          {notice.updated_at !== notice.created_at && (
                            <div className="flex items-center gap-1">
                              <RefreshCwIcon className="w-4 h-4" />
                              <span>更新时间: {formatDate(notice.updated_at)}</span>
                            </div>
                          )}
                        </div>
                        
                        <Button
                          size="sm"
                          color="primary"
                          variant="light"
                          startContent={<EyeIcon className="w-4 h-4" />}
                        >
                          查看详情
                        </Button>
                      </div>
                      
                      {notice.tags && notice.tags.length > 0 && (
                        <div className="flex gap-2 mt-3">
                          {notice.tags.map((tag, tagIndex) => (
                            <Chip key={tagIndex} size="sm" variant="flat" color="secondary">
                              {tag}
                            </Chip>
                          ))}
                        </div>
                      )}
                    </div>
                  </div>
                </CardBody>
              </Card>
            )
          })}
        </div>
      )}

      {/* Notice Detail Modal */}
      <Modal 
        isOpen={isOpen} 
        onClose={onClose}
        size="2xl"
        scrollBehavior="inside"
      >
        <ModalContent>
          {(onClose) => (
            <>
              <ModalHeader className="flex flex-col gap-1">
                <div className="flex items-center gap-3">
                  {selectedNotice && getNoticeIcon(selectedNotice)}
                  <span>{selectedNotice?.title}</span>
                </div>
                {selectedNotice && (
                  <div className="flex items-center gap-2 text-sm text-gray-500">
                    <CalendarIcon className="w-4 h-4" />
                    <span>{formatDate(selectedNotice.created_at)}</span>
                  </div>
                )}
              </ModalHeader>
              <ModalBody>
                <div className="prose dark:prose-invert max-w-none">
                  <p className="whitespace-pre-wrap leading-relaxed">
                    {selectedNotice?.content}
                  </p>
                </div>
                {selectedNotice?.tags && selectedNotice.tags.length > 0 && (
                  <div className="flex gap-2 mt-4">
                    {selectedNotice.tags.map((tag, tagIndex) => (
                      <Chip key={tagIndex} size="sm" variant="flat" color="primary">
                        {tag}
                      </Chip>
                    ))}
                  </div>
                )}
              </ModalBody>
              <ModalFooter>
                <Button color="primary" onPress={onClose}>
                  关闭
                </Button>
              </ModalFooter>
            </>
          )}
        </ModalContent>
      </Modal>
    </div>
  )
}

export default NotificationPage
