import React, { useState, useEffect } from 'react'
import {
  Dropdown,
  DropdownTrigger,
  DropdownMenu,
  DropdownItem,
  Button,
  Badge,
  Card,
  CardBody,
  Chip,
  Divider,
  ScrollShadow
} from '@heroui/react'
import { BellIcon, XIcon, AlertCircleIcon, InfoIcon } from 'lucide-react'
import { useUser } from '../contexts/UserContext'
import { useNavigate } from 'react-router-dom'
import V2BoardAPI from '../services/api'
import toast from 'react-hot-toast'

const NotificationPanel = () => {
  const { user } = useUser()
  const navigate = useNavigate()
  const [notices, setNotices] = useState([])
  const [loading, setLoading] = useState(false)
  const [unreadCount, setUnreadCount] = useState(0)
  const [isOpen, setIsOpen] = useState(false)

  useEffect(() => {
    if (user && isOpen) {
      fetchNotices()
    }
  }, [user, isOpen])

  const fetchNotices = async () => {
    if (!user) return

    setLoading(true)
    try {
      const api = new V2BoardAPI(user.baseUrl, user.token)
      const data = await api.getNotices()
      setNotices(data || [])
      
      // 计算未读通知数量（这里简单地显示所有可见的通知）
      const visibleNotices = data?.filter(notice => notice.show) || []
      setUnreadCount(visibleNotices.length)
    } catch (error) {
      console.error('Failed to fetch notices:', error)
      toast.error('获取通知失败')
    } finally {
      setLoading(false)
    }
  }

  const formatDate = (timestamp) => {
    return new Date(timestamp * 1000).toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  const getNoticeIcon = (notice) => {
    // 根据标题或内容判断通知类型
    const title = notice.title?.toLowerCase() || ''
    const content = notice.content?.toLowerCase() || ''
    
    if (title.includes('中断') || title.includes('故障') || content.includes('中断') || content.includes('故障')) {
      return <AlertCircleIcon className="w-4 h-4 text-warning-500" />
    }
    return <InfoIcon className="w-4 h-4 text-primary-500" />
  }

  const truncateContent = (content, maxLength = 100) => {
    if (!content) return ''
    return content.length > maxLength ? content.substring(0, maxLength) + '...' : content
  }

  return (
    <Dropdown 
      isOpen={isOpen}
      onOpenChange={setIsOpen}
      placement="bottom-end"
      className="w-96"
    >
      <DropdownTrigger>
        <div>
          <Badge 
            content={unreadCount > 0 ? unreadCount : null} 
            color="danger" 
            size="sm" 
            className="border-2 border-white dark:border-gray-900"
          >
            <Button 
              isIconOnly 
              variant="light" 
              size="sm"
              className="text-gray-600 dark:text-gray-300"
            >
              <BellIcon className="w-4 h-4" />
            </Button>
          </Badge>
        </div>
      </DropdownTrigger>
      
      <DropdownMenu
        aria-label="通知"
        className="p-0"
        itemClasses={{
          base: "p-0"
        }}
      >
        <DropdownItem key="notifications" className="p-0" textValue="通知列表">
          <Card className="w-full shadow-none border-none">
            <CardBody className="p-0">
              {/* Header */}
              <div className="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                  通知公告
                </h3>
                <div className="flex items-center gap-2">
                  {unreadCount > 0 && (
                    <Chip size="sm" color="danger" variant="flat">
                      {unreadCount} 条新消息
                    </Chip>
                  )}
                </div>
              </div>

              {/* Content */}
              <ScrollShadow className="max-h-96">
                {loading ? (
                  <div className="p-4 text-center">
                    <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary-500 mx-auto"></div>
                    <p className="text-sm text-gray-500 mt-2">加载中...</p>
                  </div>
                ) : notices.length === 0 ? (
                  <div className="p-8 text-center">
                    <BellIcon className="w-12 h-12 text-gray-300 mx-auto mb-3" />
                    <p className="text-gray-500 dark:text-gray-400">暂无通知</p>
                  </div>
                ) : (
                  <div className="divide-y divide-gray-200 dark:divide-gray-700">
                    {notices
                      .filter(notice => notice.show)
                      .map((notice, index) => (
                        <div key={notice.id || index} className="p-4 hover:bg-gray-50 dark:hover:bg-gray-800/50 transition-colors">
                          <div className="flex gap-3">
                            <div className="flex-shrink-0 mt-1">
                              {getNoticeIcon(notice)}
                            </div>
                            <div className="flex-1 min-w-0">
                              <div className="flex items-start justify-between gap-2">
                                <h4 className="text-sm font-medium text-gray-900 dark:text-white truncate">
                                  {notice.title}
                                </h4>
                                <span className="text-xs text-gray-500 flex-shrink-0">
                                  {formatDate(notice.created_at)}
                                </span>
                              </div>
                              <p className="text-sm text-gray-600 dark:text-gray-400 mt-1 leading-relaxed">
                                {truncateContent(notice.content)}
                              </p>
                              {notice.tags && notice.tags.length > 0 && (
                                <div className="flex gap-1 mt-2">
                                  {notice.tags.map((tag, tagIndex) => (
                                    <Chip key={tagIndex} size="sm" variant="flat" color="primary">
                                      {tag}
                                    </Chip>
                                  ))}
                                </div>
                              )}
                            </div>
                          </div>
                        </div>
                      ))}
                  </div>
                )}
              </ScrollShadow>

              {/* Footer */}
              {notices.length > 0 && (
                <div className="p-3 border-t border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800/50">
                  <Button
                    size="sm"
                    variant="light"
                    className="w-full text-primary-600 dark:text-primary-400"
                    onClick={() => {
                      setIsOpen(false)
                      navigate('/notifications')
                    }}
                  >
                    查看所有通知
                  </Button>
                </div>
              )}
            </CardBody>
          </Card>
        </DropdownItem>
      </DropdownMenu>
    </Dropdown>
  )
}

export default NotificationPanel
