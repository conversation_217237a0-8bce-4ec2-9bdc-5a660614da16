import React from 'react'
import { Link, useLocation } from 'react-router-dom'
import { 
  <PERSON>,
  Button,
  Divider
} from '@heroui/react'
import {
  HomeIcon,
  ServerIcon,
  ShoppingCartIcon,
  UserIcon,
  XIcon,
  BarChart3Icon
} from 'lucide-react'
import { cn } from '../utils/cn'

const Sidebar = ({ isOpen, onClose }) => {
  const location = useLocation()

  const menuItems = [
    {
      title: '仪表盘',
      icon: HomeIcon,
      path: '/dashboard',
      active: location.pathname === '/dashboard' || location.pathname === '/'
    },
    {
      title: '节点列表',
      icon: ServerIcon,
      path: '/servers',
      active: location.pathname === '/servers'
    },
    {
      title: '购买订阅',
      icon: ShoppingCartIcon,
      path: '/plans',
      active: location.pathname === '/plans'
    },
    {
      title: '使用统计',
      icon: BarChart3Icon,
      path: '/stats',
      active: location.pathname === '/stats'
    },
    {
      title: '我的账户',
      icon: UserIcon,
      path: '/profile',
      active: location.pathname === '/profile'
    }
  ]

  return (
    <>
      {/* Mobile overlay */}
      {isOpen && (
        <div 
          className="fixed inset-0 bg-black bg-opacity-50 z-40 lg:hidden"
          onClick={onClose}
        />
      )}
      
      {/* Sidebar */}
      <div className={cn(
        "fixed inset-y-0 left-0 z-50 w-64 bg-white dark:bg-gray-800 shadow-lg transform transition-transform duration-300 ease-in-out lg:translate-x-0 lg:static lg:inset-0",
        isOpen ? "translate-x-0" : "-translate-x-full"
      )}>
        <div className="flex flex-col h-full">
          {/* Header */}
          <div className="flex items-center justify-between h-16 px-6 border-b border-gray-200 dark:border-gray-700">
            <h1 className="text-xl font-bold text-primary-600 dark:text-primary-400">
              V2Board
            </h1>
            <Button
              isIconOnly
              variant="light"
              className="lg:hidden"
              onClick={onClose}
            >
              <XIcon className="w-5 h-5" />
            </Button>
          </div>

          {/* Navigation */}
          <nav className="flex-1 px-4 py-6 space-y-2">
            {menuItems.map((item) => {
              const Icon = item.icon
              return (
                <Link
                  key={item.path}
                  to={item.path}
                  onClick={onClose}
                  className={cn(
                    "flex items-center px-4 py-3 text-sm font-medium rounded-lg transition-colors duration-200",
                    item.active
                      ? "bg-primary-100 text-primary-700 dark:bg-primary-900 dark:text-primary-300"
                      : "text-gray-700 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-700"
                  )}
                >
                  <Icon className="w-5 h-5 mr-3" />
                  {item.title}
                </Link>
              )
            })}
          </nav>

          {/* Footer */}
          <div className="p-4 border-t border-gray-200 dark:border-gray-700">
            <Card className="p-3">
              <div className="text-center">
                <p className="text-xs text-gray-600 dark:text-gray-400">
                  V2Board Dashboard
                </p>
                <p className="text-xs text-gray-500 dark:text-gray-500 mt-1">
                  Powered by HeroUI
                </p>
              </div>
            </Card>
          </div>
        </div>
      </div>
    </>
  )
}

export default Sidebar
