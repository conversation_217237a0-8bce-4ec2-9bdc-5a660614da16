import React from 'react'
import { Link, useLocation } from 'react-router-dom'
import { 
  <PERSON>,
  Button,
  Divider
} from '@heroui/react'
import {
  HomeIcon,
  ServerIcon,
  ShoppingCartIcon,
  UserIcon,
  XIcon,
  BarChart3Icon,
  BellIcon
} from 'lucide-react'
import { cn } from '../utils/cn'

const Sidebar = ({ isOpen, onClose }) => {
  const location = useLocation()

  const menuItems = [
    {
      title: '仪表盘',
      icon: HomeIcon,
      path: '/dashboard',
      active: location.pathname === '/dashboard' || location.pathname === '/'
    },
    {
      title: '节点列表',
      icon: ServerIcon,
      path: '/servers',
      active: location.pathname === '/servers'
    },
    {
      title: '购买订阅',
      icon: ShoppingCartIcon,
      path: '/plans',
      active: location.pathname === '/plans'
    },
    {
      title: '使用统计',
      icon: BarChart3Icon,
      path: '/traffic',
      active: location.pathname === '/traffic'
    },
    {
      title: '通知公告',
      icon: BellIcon,
      path: '/notifications',
      active: location.pathname === '/notifications'
    },
    {
      title: '我的账户',
      icon: UserIcon,
      path: '/profile',
      active: location.pathname === '/profile'
    }
  ]

  return (
    <>
      {/* Mobile overlay */}
      {isOpen && (
        <div 
          className="fixed inset-0 bg-black bg-opacity-50 z-40 lg:hidden"
          onClick={onClose}
        />
      )}
      
      {/* Sidebar */}
      <div className={cn(
        "fixed inset-y-0 left-0 z-50 w-64 bg-white/95 dark:bg-gray-900/95 backdrop-blur-xl shadow-2xl transform transition-all duration-300 ease-in-out lg:translate-x-0 lg:static lg:inset-0 lg:shadow-none lg:bg-white lg:dark:bg-gray-900",
        isOpen ? "translate-x-0" : "-translate-x-full"
      )}>
        <div className="flex flex-col h-full">
          {/* Header */}
          <div className="flex items-center justify-between h-16 px-6 border-b border-gray-200/50 dark:border-gray-700/50">
            <div className="flex items-center gap-3">
              <div className="w-8 h-8 bg-gradient-to-br from-primary-500 to-primary-600 rounded-lg flex items-center justify-center">
                <span className="text-white font-bold text-sm">V2</span>
              </div>
              <h1 className="text-lg font-bold text-gray-900 dark:text-white">
                V2Board
              </h1>
            </div>
            <Button
              isIconOnly
              variant="light"
              size="sm"
              className="lg:hidden text-gray-500 dark:text-gray-400"
              onClick={onClose}
            >
              <XIcon className="w-4 h-4" />
            </Button>
          </div>

          {/* Navigation */}
          <nav className="flex-1 px-3 py-6 space-y-1">
            {menuItems.map((item) => {
              const Icon = item.icon
              return (
                <Link
                  key={item.path}
                  to={item.path}
                  onClick={onClose}
                  className={cn(
                    "flex items-center px-3 py-2.5 text-sm font-medium rounded-xl transition-all duration-200 group",
                    item.active
                      ? "bg-gradient-to-r from-primary-500 to-primary-600 text-white shadow-lg shadow-primary-500/25"
                      : "text-gray-700 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-800/50 hover:translate-x-1"
                  )}
                >
                  <Icon className={cn(
                    "w-5 h-5 mr-3 transition-transform duration-200",
                    item.active ? "text-white" : "text-gray-500 dark:text-gray-400 group-hover:text-primary-500"
                  )} />
                  <span className="truncate">{item.title}</span>
                  {item.active && (
                    <div className="ml-auto w-2 h-2 bg-white rounded-full opacity-80" />
                  )}
                </Link>
              )
            })}
          </nav>

          {/* Footer */}
          <div className="p-4 border-t border-gray-200/50 dark:border-gray-700/50">
            <div className="bg-gradient-to-r from-gray-50 to-gray-100 dark:from-gray-800 dark:to-gray-700 rounded-xl p-4">
              <div className="text-center">
                <div className="flex items-center justify-center gap-2 mb-2">
                  <div className="w-5 h-5 bg-gradient-to-br from-primary-500 to-primary-600 rounded-md flex items-center justify-center">
                    <span className="text-white font-bold text-xs">V2</span>
                  </div>
                  <p className="text-sm font-semibold text-gray-700 dark:text-gray-300">
                    V2Board
                  </p>
                </div>
                <p className="text-xs text-gray-500 dark:text-gray-400">
                  Powered by HeroUI & React
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  )
}

export default Sidebar
