import React, { useState, useEffect } from 'react'
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
  BarElement,
} from 'chart.js'
import { Line, Bar } from 'react-chartjs-2'
import { 
  Card, 
  CardBody, 
  CardHeader,
  Tabs,
  Tab,
  Spinner
} from '@heroui/react'
import { TrendingUpIcon, BarChartIcon } from 'lucide-react'
import { useUser } from '../contexts/UserContext'
import V2BoardAPI, { formatBytes } from '../services/api'

ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  Tooltip,
  Legend
)

const TrafficChart = () => {
  const { user } = useUser()
  const [trafficData, setTrafficData] = useState(null)
  const [loading, setLoading] = useState(true)
  const [activeTab, setActiveTab] = useState('line')

  useEffect(() => {
    if (user) {
      fetchTrafficData()
    }
  }, [user])

  const fetchTrafficData = async () => {
    if (!user) return

    setLoading(true)
    try {
      const api = new V2BoardAPI(user.baseUrl, user.token)
      // 模拟流量数据，实际应该从API获取
      const mockData = generateMockTrafficData()
      setTrafficData(mockData)
    } catch (error) {
      console.error('Failed to fetch traffic data:', error)
    } finally {
      setLoading(false)
    }
  }

  const generateMockTrafficData = () => {
    const days = []
    const uploadData = []
    const downloadData = []
    
    // 生成过去30天的模拟数据
    for (let i = 29; i >= 0; i--) {
      const date = new Date()
      date.setDate(date.getDate() - i)
      days.push(date.toLocaleDateString('zh-CN', { month: 'short', day: 'numeric' }))
      
      // 模拟流量数据 (字节)
      uploadData.push(Math.random() * 1024 * 1024 * 1024 * 2) // 0-2GB
      downloadData.push(Math.random() * 1024 * 1024 * 1024 * 5) // 0-5GB
    }

    return { days, uploadData, downloadData }
  }

  const chartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'top',
      },
      title: {
        display: false,
      },
      tooltip: {
        callbacks: {
          label: function(context) {
            return `${context.dataset.label}: ${formatBytes(context.parsed.y)}`
          }
        }
      }
    },
    scales: {
      y: {
        beginAtZero: true,
        ticks: {
          callback: function(value) {
            return formatBytes(value)
          }
        }
      }
    },
    interaction: {
      mode: 'index',
      intersect: false,
    },
  }

  const getChartData = () => {
    if (!trafficData) return null

    return {
      labels: trafficData.days,
      datasets: [
        {
          label: '上传流量',
          data: trafficData.uploadData,
          borderColor: 'rgb(59, 130, 246)',
          backgroundColor: 'rgba(59, 130, 246, 0.1)',
          tension: 0.4,
        },
        {
          label: '下载流量',
          data: trafficData.downloadData,
          borderColor: 'rgb(16, 185, 129)',
          backgroundColor: 'rgba(16, 185, 129, 0.1)',
          tension: 0.4,
        },
      ],
    }
  }

  if (loading) {
    return (
      <Card>
        <CardBody className="flex items-center justify-center h-64">
          <Spinner size="lg" color="primary" />
        </CardBody>
      </Card>
    )
  }

  const chartData = getChartData()

  return (
    <Card className="hover:shadow-lg transition-shadow">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between w-full">
          <h3 className="text-xl font-semibold text-gray-900 dark:text-white">
            流量使用趋势
          </h3>
          <Tabs 
            selectedKey={activeTab} 
            onSelectionChange={setActiveTab}
            size="sm"
            variant="bordered"
          >
            <Tab 
              key="line" 
              title={
                <div className="flex items-center space-x-2">
                  <TrendingUpIcon className="w-4 h-4" />
                  <span>趋势图</span>
                </div>
              }
            />
            <Tab 
              key="bar" 
              title={
                <div className="flex items-center space-x-2">
                  <BarChartIcon className="w-4 h-4" />
                  <span>柱状图</span>
                </div>
              }
            />
          </Tabs>
        </div>
      </CardHeader>
      <CardBody className="pt-0">
        <div className="h-80">
          {chartData && (
            activeTab === 'line' ? (
              <Line data={chartData} options={chartOptions} />
            ) : (
              <Bar data={chartData} options={chartOptions} />
            )
          )}
        </div>
        <div className="mt-4 grid grid-cols-2 gap-4 text-center">
          <div className="p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
            <p className="text-sm text-gray-600 dark:text-gray-400">今日上传</p>
            <p className="text-lg font-semibold text-blue-600 dark:text-blue-400">
              {trafficData ? formatBytes(trafficData.uploadData[trafficData.uploadData.length - 1]) : '0 B'}
            </p>
          </div>
          <div className="p-3 bg-green-50 dark:bg-green-900/20 rounded-lg">
            <p className="text-sm text-gray-600 dark:text-gray-400">今日下载</p>
            <p className="text-lg font-semibold text-green-600 dark:text-green-400">
              {trafficData ? formatBytes(trafficData.downloadData[trafficData.downloadData.length - 1]) : '0 B'}
            </p>
          </div>
        </div>
      </CardBody>
    </Card>
  )
}

export default TrafficChart
