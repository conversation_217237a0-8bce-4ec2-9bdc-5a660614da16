import React, { createContext, useContext, useState, useEffect } from 'react'

const UserContext = createContext()

export const useUser = () => {
  const context = useContext(UserContext)
  if (!context) {
    throw new Error('useUser must be used within a UserProvider')
  }
  return context
}

export const UserProvider = ({ children }) => {
  const [user, setUser] = useState(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState(null)

  // Load user data from localStorage on mount
  useEffect(() => {
    const savedUser = localStorage.getItem('v2board_user')
    const savedToken = localStorage.getItem('v2board_token')
    const savedBaseUrl = localStorage.getItem('v2board_url')
    
    if (savedUser && savedToken && savedBaseUrl) {
      setUser({
        ...JSON.parse(savedUser),
        token: savedToken,
        baseUrl: savedBaseUrl
      })
    }
  }, [])

  const login = async (baseUrl, email, password) => {
    setLoading(true)
    setError(null)
    
    try {
      const loginUrl = new URL('/api/v1/passport/auth/login', baseUrl).href
      const response = await fetch(loginUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email, password }),
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.message || `登录失败，状态码: ${response.status}`)
      }

      const result = await response.json()
      
      if (result.data && result.data.auth_data) {
        const userData = {
          email,
          token: result.data.auth_data,
          baseUrl
        }
        
        setUser(userData)
        
        // Save to localStorage
        localStorage.setItem('v2board_user', JSON.stringify({ email }))
        localStorage.setItem('v2board_token', result.data.auth_data)
        localStorage.setItem('v2board_url', baseUrl)
        localStorage.setItem('v2board_email', email)
        
        return userData
      } else {
        throw new Error('登录成功，但未返回认证凭证')
      }
    } catch (error) {
      setError(error.message)
      throw error
    } finally {
      setLoading(false)
    }
  }

  const logout = () => {
    setUser(null)
    setError(null)
    localStorage.removeItem('v2board_user')
    localStorage.removeItem('v2board_token')
    localStorage.removeItem('v2board_url')
  }

  const updateUserData = (newData) => {
    setUser(prev => ({ ...prev, ...newData }))
  }

  const value = {
    user,
    loading,
    error,
    login,
    logout,
    updateUserData,
    isLoggedIn: !!user
  }

  return (
    <UserContext.Provider value={value}>
      {children}
    </UserContext.Provider>
  )
}

export default UserContext
