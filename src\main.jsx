import React from 'react'
import ReactDOM from 'react-dom/client'
import { Hero<PERSON><PERSON>rovider } from '@heroui/react'
import { <PERSON><PERSON>er<PERSON>out<PERSON> } from 'react-router-dom'
import App from './App.jsx'
import './index.css'

ReactDOM.createRoot(document.getElementById('root')).render(
  <React.StrictMode>
    <BrowserRouter>
      <HeroUIProvider>
        <App />
      </HeroUIProvider>
    </BrowserRouter>
  </React.StrictMode>,
)
