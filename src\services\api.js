import axios from 'axios'

class V2BoardAPI {
  constructor(baseUrl, token) {
    this.baseUrl = baseUrl
    this.token = token
    this.client = axios.create({
      baseURL: baseUrl,
      headers: {
        'Authorization': token,
        'Content-Type': 'application/json'
      }
    })
  }

  // Generic API call method
  async call(endpoint, method = 'GET', data = null) {
    try {
      const response = await this.client.request({
        url: endpoint,
        method,
        data
      })

      if (response.data && response.data.data !== undefined) {
        return response.data.data
      } else {
        throw new Error(response.data?.message || 'API 返回数据格式不正确')
      }
    } catch (error) {
      if (error.response) {
        if (error.response.status === 401) {
          throw new Error('认证失败，请重新登录')
        }
        throw new Error(error.response.data?.message || `HTTP 错误! 状态: ${error.response.status}`)
      }
      throw new Error(error.message || '网络请求失败')
    }
  }

  // Get user subscription data
  async getSubscribe() {
    return await this.call('/api/v1/user/getSubscribe')
  }

  // Get user info
  async getUserInfo() {
    return await this.call('/api/v1/user/info')
  }

  // Get server list
  async getServerList() {
    return await this.call('/api/v1/user/server/fetch')
  }

  // Get traffic log
  async getTrafficLog() {
    return await this.call('/api/v1/user/getStat')
  }

  // Get orders
  async getOrders() {
    return await this.call('/api/v1/user/order/fetch')
  }

  // Get plans
  async getPlans() {
    return await this.call('/api/v1/user/plan/fetch')
  }

  // Reset subscription URL
  async resetSubscribeUrl() {
    return await this.call('/api/v1/user/resetSecurity', 'POST')
  }
}

// Utility functions
export const formatBytes = (bytes, decimals = 2) => {
  if (bytes === 0) return '0 Bytes'
  const k = 1024
  const dm = decimals < 0 ? 0 : decimals
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB', 'PB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i]
}

export const formatDate = (timestamp) => {
  if (!timestamp) return '长期有效'
  return new Date(timestamp * 1000).toLocaleDateString('zh-CN')
}

export const calculateDaysLeft = (timestamp) => {
  if (!timestamp) return '∞'
  const now = new Date()
  const expire = new Date(timestamp * 1000)
  const diffTime = expire - now
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
  return diffDays > 0 ? `${diffDays} 天` : '已过期'
}

export const calculateUsagePercentage = (used, total) => {
  if (total === 0) return 0
  return Math.min((used / total * 100), 100)
}

export default V2BoardAPI
