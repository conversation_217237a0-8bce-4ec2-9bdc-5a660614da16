import { heroui } from "@heroui/react";

/** @type {import('tailwindcss').Config} */
export default {
  content: [
    "./index.html",
    "./src/**/*.{js,ts,jsx,tsx}",
    "./node_modules/@heroui/theme/dist/**/*.{js,ts,jsx,tsx}"
  ],
  theme: {
    extend: {},
  },
  darkMode: "class",
  plugins: [heroui({
    themes: {
      light: {
        colors: {
          primary: {
            50: "#eff6ff",
            100: "#dbeafe", 
            200: "#bfdbfe",
            300: "#93c5fd",
            400: "#60a5fa",
            500: "#3b82f6",
            600: "#2563eb",
            700: "#1d4ed8",
            800: "#1e40af",
            900: "#1e3a8a",
            DEFAULT: "#3b82f6",
            foreground: "#ffffff",
          },
          focus: "#3b82f6",
        },
      },
      dark: {
        colors: {
          primary: {
            50: "#1e1e2e",
            100: "#2a2a3e",
            200: "#363651",
            300: "#424267",
            400: "#4e4e7e",
            500: "#5a5a95",
            600: "#6666ac",
            700: "#7272c3",
            800: "#7e7eda",
            900: "#8a8af1",
            DEFAULT: "#6666ac",
            foreground: "#ffffff",
          },
          focus: "#6666ac",
        },
      },
    },
  })],
}
